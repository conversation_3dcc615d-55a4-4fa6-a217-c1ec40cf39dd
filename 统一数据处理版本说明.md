# 可转债量化策略 - 统一数据处理版本说明

## 概述

`convertible_bond_strategy_unified.py` 是基于 `convertible_bond_strategy_batch.py` 创建的新版本，实现了更高效的数据预处理和统一筛选功能。

## 核心特性

### 1. 数据预处理
- **完整数据拼接**：将所有相关数据类型进行完整的拼接合并
- **统一数据集**：创建包含所有必要字段的完整数据集
- **数据对齐**：所有数据按交易日期对齐，处理缺失值和数据不一致问题

### 2. 统一数据集结构
```
统一数据集字段：
- BOND_CODE: 可转债代码
- TRADE_DATE: 交易日期
- YTM: 到期收益率
- PTM: 到期时间
- CONV_PREMIUM_RATIO: 转股溢价率
- STOCK_CODE: 正股代码
- BOND_PRICE: 可转债价格
- STOCK_PRICE: 正股价格
- STOCK_AMOUNT: 正股成交额
- STOCK_MARKET_CAP: 正股市值
- BOND_BALANCE: 转债余额
- BOND_RATING: 转债评级
- STOCK_NET_PROFIT: 正股净利润
```

### 3. 优化筛选逻辑
- **一次性筛选**：在完整数据集基础上一次性应用所有筛选条件
- **批量处理**：支持批量筛选多个日期
- **性能优化**：减少重复的数据加载和合并操作

## 架构设计

### 类结构

#### 1. UnifiedDataProcessor（统一数据处理器）
- **职责**：数据获取、预处理和统一数据集创建
- **核心方法**：
  - `fetch_all_raw_data()`: 获取所有原始数据
  - `create_unified_dataset()`: 创建统一数据集
  - `_fetch_*_data()`: 各类数据获取方法

#### 2. UnifiedScreener（统一筛选器）
- **职责**：在统一数据集基础上进行优化筛选
- **核心方法**：
  - `screen_bonds_for_date()`: 单日期筛选
  - `batch_screen_bonds()`: 批量筛选

#### 3. ConvertibleBondStrategyUnified（统一策略执行器）
- **职责**：协调各模块执行完整的策略流程
- **核心方法**：
  - `prepare_data()`: 数据准备
  - `run_strategy()`: 策略执行
  - `run_backtest()`: 完整回测

## 数据流程

```
1. 数据获取阶段
   ├── 交易日历
   ├── 可转债基础信息
   ├── 可转债估值数据
   ├── 可转债余额数据
   ├── 可转债评级数据
   ├── 可转债价格数据
   ├── 可转债-正股映射关系
   ├── 正股价格数据
   ├── 正股市值数据
   └── 正股财务数据

2. 数据预处理阶段
   ├── 时间字段标准化
   ├── 数据类型转换
   ├── 缺失值处理
   └── 数据验证

3. 统一数据集创建阶段
   ├── 基础数据框架构建
   ├── 映射关系合并
   ├── 价格数据合并
   ├── 时间序列数据处理
   └── 最终数据集生成

4. 筛选执行阶段
   ├── 批量日期筛选
   ├── 筛选条件应用
   └── 结果汇总

5. 策略执行阶段
   ├── 持仓矩阵构建
   ├── 价格矩阵构建
   ├── 收益率计算
   └── 结果保存
```

## 性能优化

### 1. 数据加载优化
- **一次性加载**：所有数据一次性加载到内存
- **缓存机制**：统一数据集缓存到本地文件
- **增量更新**：支持检查已有数据，避免重复获取

### 2. 筛选效率优化
- **向量化操作**：使用pandas向量化操作提高筛选效率
- **批量处理**：一次性处理多个日期的筛选
- **内存优化**：合理管理内存使用，避免数据冗余

### 3. 时间复杂度优化
- **原版本**：O(n×m×k) - n个日期，m次数据加载，k次合并
- **新版本**：O(n+m+k) - 一次数据加载，一次合并，n次筛选

## 使用方法

### 基本使用
```python
from convertible_bond_strategy_unified import ConvertibleBondStrategyUnified

# 1. 初始化策略
strategy = ConvertibleBondStrategyUnified('2024-01-01', '2024-12-31')

# 2. 准备数据（首次运行）
strategy.prepare_data()

# 3. 运行回测
strategy.run_backtest()
```

### 高级使用
```python
# 1. 分步执行
strategy = ConvertibleBondStrategyUnified('2024-01-01', '2024-12-31')

# 获取原始数据
strategy.data_processor.fetch_all_raw_data()

# 创建统一数据集
strategy.data_processor.create_unified_dataset()

# 运行策略
price_data, position_data = strategy.run_strategy()

# 计算收益
returns_df = strategy.calculate_strategy_returns(price_data, position_data)
```

## 与原版本对比

| 特性 | 原版本 | 统一数据处理版本 |
|------|--------|------------------|
| 数据获取 | 分散获取 | 一次性获取 |
| 数据合并 | 每次筛选时合并 | 预先创建统一数据集 |
| 筛选效率 | 重复数据操作 | 一次性批量筛选 |
| 内存使用 | 重复加载数据 | 数据复用 |
| 维护性 | 逻辑分散 | 逻辑集中 |
| 扩展性 | 较难扩展 | 易于扩展 |

## 优势

1. **性能提升**：减少重复数据操作，提高筛选效率
2. **内存优化**：避免重复数据加载，降低内存使用
3. **逻辑清晰**：数据预处理与筛选逻辑分离
4. **易于维护**：统一的数据结构便于维护和调试
5. **扩展性强**：新增筛选条件或数据字段更容易

## 注意事项

1. **首次运行**：需要先运行 `prepare_data()` 获取数据并创建统一数据集
2. **内存需求**：统一数据集会占用更多内存，适合内存充足的环境
3. **数据更新**：如需更新数据，需要重新运行数据准备流程
4. **兼容性**：与原版本的接口保持兼容，可以无缝切换

## 文件结构

```
convertible_bond_strategy_unified.py
├── UnifiedDataProcessor      # 统一数据处理器
├── UnifiedScreener          # 统一筛选器
├── ConvertibleBondStrategyUnified  # 统一策略执行器
└── main()                   # 主程序入口
```

## 测试验证

运行测试脚本验证功能：
```bash
python test_unified_strategy.py
```

测试覆盖：
- ✅ 基本功能测试
- ✅ 统一筛选器测试
- ✅ 数据处理器测试
- ✅ 集成功能测试

## 总结

统一数据处理版本通过预处理和统一数据集的设计，显著提升了数据处理效率和代码维护性，同时保持了与原版本的功能兼容性。适合对性能有较高要求的量化策略应用场景。
