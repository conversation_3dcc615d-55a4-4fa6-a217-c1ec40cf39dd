﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
CHANGE_DT,变动日期(除权日),Change day,VARCHAR2(8),变动股份的登记日期。如股票分红送转，则为红股除权日；如增发则为新增股份的登记日。,
ANN_DT,公告日,Announcement day,VARCHAR2(8),股本变动事件公告的发布日期。,
S_ISSUEAMOUNT,发行股份数,Number of shares issued,"NUMBER(20,4)",对公司资产、利润分享等享有优先权的股份数,万股
COMPID,公司ID,Company ID,VARCHAR2(10),万得自定义的用来识别公司的ID,
CHANGE_DT_LISTDATE,变动日期(上市日),Listing Date (Ex-rights),VARCHAR2(8),变动股份的上市日期。如股票分红送转，则为红股上市日；如增发则为新增股份的上市日。,
IS_VALID,是否有效,Is it effective,"NUMBER(5,0)",用来区分除权日相同时，是否为公司公告公布的最新股份数,
