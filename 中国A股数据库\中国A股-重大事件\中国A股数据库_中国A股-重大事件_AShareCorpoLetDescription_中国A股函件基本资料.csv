﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
S_INFO_WINDCODE,Wind代码,wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_INFO_COMPCODE,公司ID,Company id,VARCHAR2(10),万得自定义的用来识别公司的唯一编码,
LETTERS_EVENT_ID,函件事件ID,Letters event id,VARCHAR2(20),发函公告日期后6位+AB+3位数字,
LETTERS_DEPARTMENT,发函部门,Department of sending letters,VARCHAR2(100),记录发函部门的名称。,
LETTERS_DEPARTMENT_ID,发函单位ID,Letters Department ID,VARCHAR2(10),记录发函部门所在单位的公司ID。,
LETTERS_NUMBER,函件文号,Letter No.,VARCHAR2(200),交易所发出的函件公告中所公布的文件号码,
LETTERS_TITLE,函件标题,Letter title,VARCHAR2(500),发函公告的标题。,
LETTERS_TYPE_CODE,函件类型代码,Letter Type Code,"NUMBER(9,0)",关联AShareTypeCode表，前3位为339,
LETTERS_ANN_DT,发函公告日,Letter Announcement Date,VARCHAR2(8),若为交易所先公告，即发函日期，若上市公司先公告，则为上市公司公告日,
REQUEST_DATE,要求回函日期,Date of request for reply,VARCHAR2(8),交易所对企业发出的函件后要求企业回函的日期,
EXTENSION_REPLY_DATE,申请延期回复日期,Reply date of application for extension,VARCHAR2(8),若上市公司存在晚于要求回函日期答复时，公布的延期回复日期,
IS_EXTENSION_REPLY,是否延期回复,Delay reply or not,"NUMBER(1,0)",若为1，则上市公司存在申请延期回复，否则为0,
REPLY_ANNOUCEMENT_DATE,回函公告日,Reply announcement date,VARCHAR2(8),若为交易所先公告，为交易所回函公告日，若上市公司先公告，则为上市公司公告日,
SUPPLEMENTARY_DATE,补充回函公告日,Supplementary reply announcement date,VARCHAR2(8),企业对交易所函件进行补充回复的日期,
EXTENSION_REPLY_ADATE,申请延期回复公告日,Announcement date of application for extension of reply,VARCHAR2(8),发布申请延期回复函件公告的日期,
OBJECT_ID,对象ID,object id,VARCHAR2(100),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
