﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_HOLDER_NAME,股东名称,Shareholder name,VARCHAR2(200),记录上市公司公布的股东名称,
S_PH_STARTDATE,增持计划起始日期,Increase plan start date,VARCHAR2(8),记录上市公司股东增持计划的截止日期,
S_PH_ENDDATE,增持计划截止日期,Overdue plan deadline,VARCHAR2(8),记录上市公司股东增持计划的截止日期,
S_PH_CONDITIONORNOT,是否无条件增持,Whether to unconditionally increase,"NUMBER(5,0)",1 是；0 否,
S_PH_TRIGGERPRICE,增持触发价格,Overweight trigger price,"NUMBER(20,4)",触发增持的最高价，触发条件有两个，加工两条记录(例如600550),元
S_PH_CONTINUOUSDAYS,连续天数,Continuous days,"NUMBER(20,4)",记录连续天数,
S_PH_CALCULATEDAYS,计算天数,Calculated days,"NUMBER(20,4)",记录计算天数,
S_PH_CALCULATEPRICEMODE,价格计算方式,Price calculation method,VARCHAR2(80),记录价格是按什么方式计算,
S_PH_SHARENUMDOWNLIMIT,增持股数下限(万股),"The lower limit of the number of shares held (10,000 shares)","NUMBER(20,4)",记录上市公司股东增持股数的下限,万股
S_PH_SHARENUMUPLIMIT,增持股数上限(万股),"Maximum number of shares held (10,000 shares)","NUMBER(20,4)",记录上市公司股东增持股数的上限,万股
S_PH_INTENDPUTMONEYDOWNLIMIT,拟投入金额下限(亿元),The minimum amount of the proposed investment (100 million yuan),"NUMBER(20,4)",记录上市公司股东拟投入金额下限,亿元
S_PH_INTENDPUTMONEYUPLIMIT,拟投入金额上限(亿元),The maximum amount of the proposed investment (100 million yuan),"NUMBER(20,4)",记录上市公司股东拟投入金额上限,亿元
S_PH_PRICEUPLIMIT,增持价格上限,Overweight price limit,"NUMBER(20,4)",空表示无增持价格限制,元
