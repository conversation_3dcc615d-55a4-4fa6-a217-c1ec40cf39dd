﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_WINDCODE,Wind代码,WIND CODE,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_INFO_CODE,交易代码,trasaction code,VARCHAR2(40),交易所公布的证券代码，特殊情况下可能是万得自编代码；交易所公布代码(特殊情款(无公布代码或公布代码需修改)为万得自编代码)；交易所公布的用来标识某个证券的编码；基金产品在市场中对应的唯一标识代码；万得自定义的用来识别证券的唯一编码(去除后缀)；交易所公布代码，若未公布为万得自编代码；,
S_INFO_NAME,证券简称,Securities short name,VARCHAR2(100),公司名称的简称；交易所披露该证券的中文简称；每只证券中文简称；该证券的中文简称；证券在公告中公布的简称；证券的中文简称；交易所公布证券的中文简称，若未公布为万得自编简称；,
S_INFO_COMPNAME,指数名称,Index name,VARCHAR2(100),综合反映一类集合证券总体走势的统计指标的名字；根据某些采样股票或债券的价格所设计并计算出来的统计数据的名称；是指官网公布的名称；指数的中文名称；,
S_INFO_EXCHMARKET,交易所,Exchange,VARCHAR2(40),既定的各个交易所对应的交易所代码；规范后的交易所代码；该证券所在交易所的英文简称；全球市场现有交易所的英文名称；各交易所英文代码缩写；,
S_INFO_INDEX_BASEPER,基期,Base period,VARCHAR2(8),统计中计算指数指标时，作为对比基础的日期；指数计算动态指标时作为对比标准的日期；是指计算股价指数的基准也就是说，要将成分股结合或平均起来，首先必须确定一个基期或基日，以它作为计算股价指数的基准；指数基础起始期的值；,
S_INFO_INDEX_BASEPT,基点,base point,"NUMBER(20,4)",指数成立当天，作为这个指数的成份所有加权后的现值的总额；计算指数该变量的度量单位的值；衡量指数利率变动的最小计量；衡量利率变动的最小计量单位；,
S_INFO_LISTDATE,发布日期,Release date,VARCHAR2(8),指数所有的数据通过各种媒介展示出来的日期；编制指数发布并正常运作的起始日期；指数正式推向了市场，可供投资者查询的日期；指数发布日期；,
S_INFO_INDEX_WEIGHTSRULE,加权方式,Weighting method,VARCHAR2(10),将不同数值乘以不同的系数来计算对比数据的方式；计算指数指标时采用的加权计算方式；根据各种样本股票的相对重要性进行加权的方式；指数计算的加权方式；,
S_INFO_PUBLISHER,发布方,Publisher,VARCHAR2(100),通过各种媒介展示指数所有数据的经济实体；编制指数并发布的主体；发布指数的单位；发布该指数的主体；,
S_INFO_INDEXCODE,指数类别代码,Index category code,"NUMBER(9,0)",万得自定义的用于识别指数品类的编码；划分编制指数的主要证券类型在万得数据库中的编码；指数类别关联的代码；,
S_INFO_INDEXSTYLE,指数风格(废弃),Index style,VARCHAR2(40),反映指数在市场上所属的特定风格类型或者投资特征；万得内部定义的当前指数呈现的有代表性的形式；指的是指数的某种特定风格或投资特征；指数的不同特征说明；,
INDEX_INTRO,指数简介,Index introduction,VARCHAR2(4000),综合反映一类集合证券总体走势的统计指标的简要介绍；编制主体对当前指数样本空间编制方法等的简要介绍；指数重要内容的介绍；指数概况说明；,
WEIGHT_TYPE,权重类型,Weight type,"NUMBER(9,0)",万得自定义的用于识别商用付费第三方指数类型的编码；划分商用指数类型的编码；商用指数类型代码；,
EXPIRE_DATE,终止发布日期,End of release date,VARCHAR2(8),指数停止通过各种媒介展示所有的数据的日期；指数发布并正常运作的截止日期；指数停止发布的日期；指数终止发布日期；,
INCOME_PROCESSING_METHOD,收益处理方式,Income processing method,VARCHAR2(20),ESG指标分类；万得自定义的用来进行可枚举业务字段分类的具体名称；根据一定规则进行分类后的具体分类名称；编码对应的类型中文名称；各分类下的类型名称；,
CHANGE_HISTORY,变更历史(废弃),Change history,VARCHAR2(100),该指数作出变动的历史记录,
S_INFO_PINYIN,简称拼音,PINYIN,VARCHAR2(40),标的物证券简称的拼音首字母；证券产品或实体公司简化名称的拼音首字母组合；证券简称的拼音首字母；,
WEIGHT_TYPE_NAME,权重类型名称,Weight type name,VARCHAR2(100),指数成份在该指数所有成份中所占比重值的计算方式类型；评价指数指标在整体时长评价中相对重要程度的类型；总股本加权、流通股本加权、流通A股加权等；,
S_INFO_INDEXTYPE,指数类别,Index category type,VARCHAR2(40),万得自定义的用于识别指数品类的编码；划分编制指数的主要证券类型；指数分类；,
INDEX_REGION_CODE,指数区域代码,,"NUMBER(9,0)",指数所发布的地区或者国家代码；指数所在地区在万得库中对应的唯一编码ID；指数对应的区域代码；,
EXPONENTIAL_SCALE_CODE,指数规模代码,,"NUMBER(9,0)",万得自定义的用于识别指数规模价值的编码；反应指数所属体量大小范围的指标；指数规模关联的代码；,
MARKET_OWN_CODE,所属市场代码,,"NUMBER(9,0)",万得自定义的用于识别指数所在的交易市场的编码；指数所在市场在万得库中对应的唯一编码ID；指数所属市场代码；,
S_INFO_COMPENAME,指数英文名称,,VARCHAR2(200),综合反映一类集合证券总体走势的统计指标的英文名字；使用英文表述的指数的名称；指数的英文名称；,
