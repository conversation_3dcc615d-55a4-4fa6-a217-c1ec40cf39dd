﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
EVENT_ID,并购事件ID,M&A event ID,VARCHAR2(20),万得自定义的用来识别交易并购事件的唯一编码,
AGENCY_NAME,中介机构公司名称,Intermediary company name,VARCHAR2(200),公司注册的合法名称；公司或企业组织的名称；基金产品的公布名称；证券或公司对应的中文全称；公司的称呼；,
S_INFO_COMPCODE,中介机构公司ID,Intermediary company ID,VARCHAR2(40),Wind自编代码，公司唯一性,
AGENCY_TYPCODE,中介机构类型代码,Intermediary type code,"NUMBER(9,0)",附注,
OBJECT_TYPCODE,中介机构服务对象类型代码,Intermediary service object type code,"NUMBER(9,0)",附注,
OBJECT_COMPNAME,中介机构服务对象（公司）名称,Intermediary service object (company) name,VARCHAR2(200),公司注册的合法名称；公司或企业组织的名称；基金产品的公布名称；证券或公司对应的中文全称；公司的称呼；,
S_INFO_COMPCODE2,中介机构服务对象（公司）ID,Intermediary Service Object (Company) ID,VARCHAR2(40),Wind自编代码，公司唯一性,
START_DT,起始日期,Start date,VARCHAR2(8),披露聘请该中介机构的最早公告的发布日期,
END_DT,终止日期,End date,VARCHAR2(8),披露不再聘请该中介机构的公告的发布日期,
CUR_SIGN,最新标志,Latest sign,"NUMBER(1,0)","1代表是最新,否则为0",
