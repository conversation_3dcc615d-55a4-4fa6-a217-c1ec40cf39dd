#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
对比原版本和批量数据版本的可转债策略
"""

import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_strategy_results(file_pattern: str, strategy_name: str):
    """加载策略结果"""
    results_folder = Path('Results')
    if not results_folder.exists():
        print(f"Results文件夹不存在")
        return None
    
    # 查找匹配的文件
    files = list(results_folder.glob(f'*{file_pattern}*.xlsx'))
    if not files:
        print(f"没有找到{strategy_name}的结果文件")
        return None
    
    # 使用最新的文件
    latest_file = max(files, key=lambda x: x.stat().st_mtime)
    print(f"加载{strategy_name}结果: {latest_file.name}")
    
    try:
        df = pd.read_excel(latest_file)
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        return df
    except Exception as e:
        print(f"加载{strategy_name}结果失败: {e}")
        return None

def calculate_performance_metrics(returns_df: pd.DataFrame) -> dict:
    """计算策略表现指标"""
    if returns_df is None or returns_df.empty:
        return {}
    
    try:
        # 基本指标
        final_nav = returns_df['net_value'].iloc[-1]
        total_return = (final_nav - 1) * 100
        
        # 日收益率统计
        daily_returns = returns_df['daily_return'].dropna()
        if len(daily_returns) > 0:
            annual_return = daily_returns.mean() * 252 * 100
            volatility = daily_returns.std() * np.sqrt(252) * 100
            sharpe = annual_return / volatility if volatility > 0 else 0
            
            # 最大回撤
            nav_series = returns_df['net_value']
            running_max = nav_series.expanding().max()
            drawdown = (nav_series - running_max) / running_max
            max_drawdown = drawdown.min() * 100
            
            # 胜率
            win_rate = (daily_returns > 0).mean() * 100
            
            return {
                'final_nav': final_nav,
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'trading_days': len(returns_df)
            }
        else:
            return {
                'final_nav': final_nav,
                'total_return': total_return,
                'trading_days': len(returns_df)
            }
    except Exception as e:
        print(f"计算表现指标失败: {e}")
        return {}

def compare_strategies():
    """对比两个策略版本"""
    print("=" * 80)
    print("可转债策略版本对比分析")
    print("=" * 80)
    
    # 加载原版本结果
    original_results = load_strategy_results('月频转债策略收益率', '原版本')
    
    # 加载批量数据版本结果
    batch_results = load_strategy_results('批量数据转债策略收益率', '批量数据版本')
    
    if original_results is None and batch_results is None:
        print("没有找到任何策略结果文件")
        return
    
    # 计算表现指标
    print("\n" + "=" * 60)
    print("策略表现对比")
    print("=" * 60)
    
    if original_results is not None:
        original_metrics = calculate_performance_metrics(original_results)
        print("\n原版本策略表现:")
        print("-" * 40)
        for key, value in original_metrics.items():
            if key in ['final_nav']:
                print(f"{key}: {value:.4f}")
            elif key in ['total_return', 'annual_return', 'volatility', 'max_drawdown', 'win_rate']:
                print(f"{key}: {value:.2f}%")
            elif key in ['sharpe_ratio']:
                print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")
    
    if batch_results is not None:
        batch_metrics = calculate_performance_metrics(batch_results)
        print("\n批量数据版本策略表现:")
        print("-" * 40)
        for key, value in batch_metrics.items():
            if key in ['final_nav']:
                print(f"{key}: {value:.4f}")
            elif key in ['total_return', 'annual_return', 'volatility', 'max_drawdown', 'win_rate']:
                print(f"{key}: {value:.2f}%")
            elif key in ['sharpe_ratio']:
                print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")
    
    # 绘制净值曲线对比
    if original_results is not None or batch_results is not None:
        plot_comparison(original_results, batch_results)
    
    # 数据处理方式对比
    print("\n" + "=" * 60)
    print("数据处理方式对比")
    print("=" * 60)
    
    print("\n原版本特点:")
    print("- 实时查询数据库")
    print("- 按需获取数据")
    print("- 适合小规模测试")
    print("- 网络依赖性强")
    
    print("\n批量数据版本特点:")
    print("- 预先获取所有数据")
    print("- 本地存储数据")
    print("- 适合大规模回测")
    print("- 支持离线分析")
    print("- 避免重复查询")
    
    # 使用建议
    print("\n" + "=" * 60)
    print("使用建议")
    print("=" * 60)
    
    print("\n选择原版本的情况:")
    print("- 快速验证策略逻辑")
    print("- 短期回测（3个月内）")
    print("- 数据存储空间有限")
    print("- 网络连接稳定")
    
    print("\n选择批量数据版本的情况:")
    print("- 长期历史回测（1年以上）")
    print("- 多次重复分析")
    print("- 网络连接不稳定")
    print("- 需要离线分析")
    print("- 大规模参数优化")

def plot_comparison(original_results, batch_results):
    """绘制净值曲线对比图"""
    try:
        fig, ax = plt.subplots(figsize=(12, 8))
        
        if original_results is not None:
            ax.plot(original_results['date'], original_results['net_value'], 
                   label='原版本', linewidth=2, color='blue')
        
        if batch_results is not None:
            ax.plot(batch_results['date'], batch_results['net_value'], 
                   label='批量数据版本', linewidth=2, color='red', linestyle='--')
        
        ax.set_title('可转债策略净值曲线对比', fontsize=16, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('净值', fontsize=12)
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        import matplotlib.dates as mdates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # 保存图片
        save_path = Path('Results') / f'策略对比_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"\n对比图表已保存: {save_path}")
        
        plt.show()
        
    except Exception as e:
        print(f"绘制对比图失败: {e}")

def analyze_data_efficiency():
    """分析数据获取效率"""
    print("\n" + "=" * 60)
    print("数据获取效率分析")
    print("=" * 60)
    
    # 检查批量数据文件
    data_folder = Path('BatchData')
    if data_folder.exists():
        files = list(data_folder.glob('*.pkl'))
        if files:
            total_size = sum(f.stat().st_size for f in files)
            print(f"\n批量数据存储:")
            print(f"- 文件数量: {len(files)}")
            print(f"- 总大小: {total_size / (1024*1024):.1f} MB")
            print(f"- 平均文件大小: {total_size / len(files) / (1024*1024):.1f} MB")
            
            print(f"\n数据文件详情:")
            for file in sorted(files):
                size_mb = file.stat().st_size / (1024 * 1024)
                print(f"- {file.name}: {size_mb:.1f} MB")
        else:
            print("没有找到批量数据文件")
    else:
        print("批量数据目录不存在")

def main():
    """主程序"""
    print("可转债策略版本对比分析工具")
    print("=" * 60)
    
    while True:
        print("\n请选择分析项目:")
        print("1. 策略表现对比")
        print("2. 数据获取效率分析")
        print("3. 完整对比分析")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            compare_strategies()
        elif choice == '2':
            analyze_data_efficiency()
        elif choice == '3':
            compare_strategies()
            analyze_data_efficiency()
        elif choice == '4':
            print("退出分析工具")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
