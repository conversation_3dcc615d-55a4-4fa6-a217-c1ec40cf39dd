﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_COMPCODE,公司id,Company id,VARCHAR2(40),万得自定义的用来识别公司的唯一编码；万得基金自定义唯一编码；,
REPORT_PERIOD,报告期,Reporting period,VARCHAR2(8),反映企业经营成果、现金流量的特定时期根据长短不同分为年度、半年度、季度；该财务附注数据对应的截止日期；,
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期；该财务附注数据来源材料的披露日期；,
STATEMENT_TYPE,报表类型,Report type,VARCHAR2(80),根据编制的会计主体，划分为母公司报表和合并报表；该财务附注数据对应的统计类型；,
ITEM_DATA,数据内容,Data content,VARCHAR2(40),整体公告内容的概括性标识；该财务附注数据对应的科目；,
ITEM_TYPE_CODE,项目类别代码,Item category code,VARCHAR2(4),公告数据内容的存在及变动状态的标识；万得自定义项目类别代码；,
ANN_ITEM,项目公布名称,Project announcement name,VARCHAR2(400),公告中公布的具体项目名称；项目在报告中的公布名称；,
ITEM_AMOUNT,项目金额,Item Amount,"NUMBER(20,4)",用于区分新旧准则的标识；项目在公告中披露的金额；,元
ITEM_NAME,项目容错名称,Project fault tolerance name,VARCHAR2(100),公告中公布的具体项目名称对应的万得标准名称；万得对科目的容错后名称；,
ITEM_TYPE_NAME,项目类别名称,Item type Name,VARCHAR2(300),项目根据某些维度的分类名称,
