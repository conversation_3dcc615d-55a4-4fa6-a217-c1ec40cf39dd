#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查看策略回测结果
"""

import pandas as pd
import numpy as np

def check_strategy_results():
    """查看策略结果"""
    print("=" * 60)
    print("可转债策略回测结果分析")
    print("=" * 60)
    
    try:
        # 读取收益率数据
        returns_file = 'Results/月频转债策略收益率_20250706_165727.xlsx'
        df = pd.read_excel(returns_file)
        
        print("策略收益率数据:")
        print(df.head(10))
        print(f"\n数据形状: {df.shape}")
        
        if not df.empty and 'net_value' in df.columns:
            final_nav = df['net_value'].iloc[-1]
            total_return = (final_nav - 1) * 100
            
            print(f"\n=== 策略表现 ===")
            print(f"最终净值: {final_nav:.4f}")
            print(f"累计收益率: {total_return:.2f}%")
            
            # 计算一些基本统计
            if 'daily_return' in df.columns:
                daily_returns = df['daily_return'].dropna()
                if len(daily_returns) > 0:
                    annual_return = daily_returns.mean() * 252 * 100
                    volatility = daily_returns.std() * np.sqrt(252) * 100
                    sharpe = annual_return / volatility if volatility > 0 else 0
                    
                    print(f"年化收益率: {annual_return:.2f}%")
                    print(f"年化波动率: {volatility:.2f}%")
                    print(f"夏普比率: {sharpe:.2f}")
        
        # 读取持仓数据
        position_file = 'Results/月频转债策略持仓_20250706_165727.xlsx'
        pos_df = pd.read_excel(position_file, index_col=0)
        
        print(f"\n=== 持仓数据 ===")
        print(f"持仓矩阵形状: {pos_df.shape}")
        print(f"涉及可转债数量: {(pos_df > 0).any().sum()}")
        
        # 显示每个调仓日的持仓数量
        position_counts = (pos_df > 0).sum(axis=1)
        non_zero_positions = position_counts[position_counts > 0]
        
        print(f"\n各调仓日持仓数量:")
        for date, count in non_zero_positions.items():
            print(f"{date}: {count}只")
            
    except Exception as e:
        print(f"读取结果文件失败: {e}")

if __name__ == "__main__":
    check_strategy_results()
