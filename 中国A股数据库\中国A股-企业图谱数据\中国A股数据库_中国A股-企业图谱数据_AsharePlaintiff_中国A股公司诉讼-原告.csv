﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),万得自定义的用来识别公司的唯一编码,
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(100),万得自定义的用来识别公司的唯一编码,
S_INFO_DIMENSION,一级关系维度名称,Tier 1 relationship dimension name,VARCHAR2(100),,
S_INFO_DIMENSION1,二级关系维度名称,Secondary relationship dimension name,VARCHAR2(100),,
S_INFO_COMP_NAME,诉讼公司名称,Litigation company name,VARCHAR2(100),发生或完成某种事件的相关公司名称,
S_INFO_COMP_SNAME,诉讼公司中文简称,Litigation Company Chinese Abbreviation,VARCHAR2(100),公司公布的中文简称；公司中文简称；公司或企业组织的中文名称缩写；公司的简化名称；公司或证券的中文简称；公司名称的中文简短称呼；,
S_INFO_COMPCODE1,诉讼公司ID,Litigation company ID,VARCHAR2(100),万得自定义的用来识别公司的唯一编码,
S_INFO_CASE_TYPE,案件类型,Case type,VARCHAR2(10),根据案件性质和所依据的法律分类；诉讼所属的类型；,
ANN_DATE,公告日期,Announcement Date,VARCHAR2(8),公告发布当天的日期,
LITIGATION_EVENTS_ID,诉讼事件ID,Litigation Event ID,VARCHAR2(40),万得自定义的用来识别诉讼事件的唯一编码,
