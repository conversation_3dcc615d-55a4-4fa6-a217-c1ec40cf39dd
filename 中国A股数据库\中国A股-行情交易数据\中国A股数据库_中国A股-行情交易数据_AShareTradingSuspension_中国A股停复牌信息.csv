﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_DQ_SUSPENDDATE,停牌日期,Suspension date,VARCHAR2(8),上市证券的停牌日期，记录年月日；公司股票暂停交易的日期；证券停牌的停牌日期；暂停上市/停止交易的起始日期；,
S_DQ_SUSPENDTYPE,停牌类型代码,Suspension type code,"NUMBER(9,0)",上市证券的停牌类型对应的原始代码；万得根据停牌的长短自定义的用来区分停牌类型的编码；万得自定义的用来表示停牌类型的编码；停牌的类型编码；,
S_DQ_RESUMPDATE,复牌日期,Resumption date,VARCHAR2(8),上市证券的复牌日期；公司股票停牌一段时间后恢复交易的日期；停牌证券的复牌日期；恢复上市/恢复交易的日期；,
S_DQ_CHANGEREASON,停牌原因,Reason for suspension,VARCHAR2(400),上市证券的停牌原因；公司股票暂停交易的原因；证券停牌的停牌原因；暂停上市/停止交易的原因；,
S_DQ_TIME,停复牌时间,Stop trading time,VARCHAR2(200),上市证券的停牌时间，记录时分；记录公司于当天一段时间内从暂停交易到可交易的时间范围；证券的停复牌时间；暂停上市/停止交易的具体时间区间；,
S_DQ_CHANGEREASONTYPE,停牌原因代码,Suspension reason code,"NUMBER(9,0)",上市证券的停牌原因对应的原始代码；万得根据停牌的原因自定义的用来区分停牌事项的编码；万得自定义的用来表示停牌原因的编码；暂停上市的原因代码；,
