﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_WINDCODE,Wind代码,Wind Code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_PROFITNOTICE_DATE,最新公告日期,Latest announcement date,VARCHAR2(8),公告发布当天的日期,
S_PROFITNOTICE_PERIOD,报告期,Report Period,VARCHAR2(8),反映企业经营成果、现金流量的特定时期根据长短不同分为年度、半年度、季度,
S_PROFITNOTICE_STYLE,业绩预告类型代码,Code of Earnings Preannouncement Type,"NUMBER(9,0)","业绩预告类型：          不确定 454001000
略减 454002000
略增 454003000
扭亏 454004000
其他 454005000
首亏 454006000
续亏 454007000
续盈 454008000
预减 454009000
预增 454010000
持平 454011000",
S_PROFITNOTICE_SIGNCHANGE,是否变脸,Change or Not,VARCHAR2(10),1：是；0：否,
S_PROFITNOTICE_CHANGEMIN,预告净利润变动幅度下限（%）,Minimum Change of Estimated Net Profit (%),"NUMBER(20,4)",对于净利润金额同比变动幅度预计的最低值,
S_PROFITNOTICE_CHANGEMAX,预告净利润变动幅度上限（%）,Maximum Change of Estimated Net Profit (%),"NUMBER(20,4)",对于净利润金额同比变动幅度预计的最高值,
S_PROFITNOTICE_NETPROFITMIN,预告净利润下限（万元）,"Minimum of Estimated Net Profit (CNY 10,000)","NUMBER(20,4)",对于净利润金额预计的最低值,
S_PROFITNOTICE_NETPROFITMAX,预告净利润上限（万元）,"Maximum of Estimated Net Profit (CNY 10,000)","NUMBER(20,4)",对于净利润金额预计的最高值,
S_PROFITNOTICE_NUMBER,公布次数,Times of publication,"NUMBER(15,4)",,
S_PROFITNOTICE_FIRSTANNDATE,首次公告日,First announcement day,VARCHAR2(8),首次披露本报告期业绩预告内容的公告日期,
S_PROFITNOTICE_ABSTRACT,业绩预告摘要,Summary of performance notice,VARCHAR2(200),对于净利润金额预计的最低值,
S_PROFITNOTICE_REASON,业绩变动原因,Reasons For Performance Change,VARCHAR2(3000),,
S_PROFITNOTICE_NET_PARENT_FIRM,上年同期归母净利润,Net profit of the parent company in the same period of last year,"NUMBER(20,4)",业绩预告中直接公布的上年同期归母净利润,
S_PROFITNOTICE_CHANGEMIN_DED,预告扣非净利润变动幅度下限,Minimum Change Of Estimated Net Profit (%)(excluding Non-recurring Gains And Losses),"NUMBER(20,4)",对于扣除非经常性损益后的归属于母公司股东的净利润金额同比变动幅度预计的最低值,
S_PROFITNOTICE_CHANGEMAX_DED,预告扣非净利润变动幅度上限,Maximum Change Of Estimated Net Profit (%)(excluding Non-recurring Gains And Losses),"NUMBER(20,4)",对于扣除非经常性损益后的归属于母公司股东的净利润金额同比变动幅度预计的最高值,
S_PROFITNOTICE_NI_MIN_DED,预告扣非净利润下限,"Minimum Of Estimated Net Profit (CNY 10,000)(excluding Non-recurring Gains And Losses)","NUMBER(20,8)",对于扣除非经常性损益后的归属于母公司股东的净利润金额预计的最低值,
S_PROFITNOTICE_NI_MAX_DED,预告扣非净利润上限,"Maximum Of Estimated Net Profit (CNY 10,000)(excluding Non-recurring Gains And Losses)","NUMBER(20,8)",对于扣除非经常性损益后的归属于母公司股东的净利润金额预计的最高值,
S_PROFITNOTICE_NETPROFIT_DED,上年同期扣非净利润,Net Profit In The Same Period Of Last Year (excluding Non-recurring Gains And Losses),"NUMBER(20,8)",公告中直接公布的上年同期扣除非经常性损益后的归属于母公司股东的净利润金额,
S_PROFITNOTICE_BASICEARNMIN,预告基本每股收益下限,The Lower Limit Of The Forecast Basic Earnings Per Share,"NUMBER(20,4)",对于基本每股收益预计的最低值,元/股
S_PROFITNOTICE_BASICEARNMAX,预告基本每股收益上限,The Upper Limit Of The Forecast Basic Earnings Per Share,"NUMBER(20,4)",对于基本每股收益预计的最高值,元/股
S_PROFITNOTICE_LASTBASICEARN,上年同期基本每股收益,Basic Earnings Per Share For The Same Period Of The Previous Year,"NUMBER(20,4)",公告中直接公布的基本每股收益,元/股
S_PROFITNOTICE_INCOMEMIN,预告营业收入下限,he Lower Limit Of The Operating income,"NUMBER(20,8)",最新一次业绩预告中直接公布的预告营业收入下限,万元
S_PROFITNOTICE_INCOMEMAX,预告营业收入上限,The Upper Limit Of The Operating income,"NUMBER(20,8)",最新一次业绩预告中直接公布的预告营业收入上限,万元
S_PROFITNOTICE_LASTYEARINCOME,上年同期营业收入,Operating income For The Same Period Of The Previous Year,"NUMBER(20,8)",最新一次业绩预告中直接公布的上年同期营业收入,万元
DEDUCTEDSALESMIN,预告扣除后营业收入下限,Lower Limit of Business Income After Deduction of Notice,"NUMBER(20,8)",最新一次业绩预告中直接公布的预告扣除后营业收入下限,万元
DEDUCTEDSALESMAX,预告扣除后营业收入上限,Upper Limit of Operating Income After Deduction of Notice,"NUMBER(20,8)",最新一次业绩预告中直接公布的预告扣除后营业收入上限,万元
LASTYEARDEDUCTEDSALES,上年同期扣除后营业收入,Operating Income Deducted from the Same Period Last Year,"NUMBER(20,8)",最新一次业绩预告中直接公布的上年同期扣除后营业收入,万元
DEDUCTEDEARNMIN,预告扣非后基本每股收益下限,Lower Limit of Basic Earnings Per Share After Pre-Announced Deduction,"NUMBER(20,4)",最新一次业绩预告中直接公布的预告扣非后基本每股收益下限,元/股
DEDUCTEDEARNMAX,预告扣非后基本每股收益上限,Upper Limit of Basic Earnings Per Share After Pre-Announced Deduction,"NUMBER(20,4)",最新一次业绩预告中直接公布的预告扣非后基本每股收益上限,元/股
ASTYEARDEDUCTEDEARN,上年同期扣非后基本每股收益,Basic Earnings Per Share After Pre-Announced Deduction for the Same Period Last Year,"NUMBER(20,4)",最新一次业绩预告中直接公布的上年同期扣非后基本每股收益,元/股
COMP_ID,公司ID,Company ID,VARCHAR2(10),万得自定义的用来识别公司的唯一编码,
