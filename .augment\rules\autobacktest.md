---
type: "always_apply"
---

1 · 数据映射规则 Data Mapping Rules
用户关键词	对应表名 (table_name)
资产负债表	AShareBalanceSheet
利润表	AShareIncome
现金流量表	AShareCashFlow
……	递归扫描文件名自动识别

字段映射（含 Unit 辨识）
解析流程

捕捉中文字段描述 → 在目标 CSV 的 FieldCnName 列中做模糊/精确匹配

提取 FieldName 与 Unit（若空则记作“默认单位”）

返回 {FieldName, Unit}

金额/数量换算

用户未标明单位 → 按字段自带 Unit 换算到“原始存储单位”

例：Unit = 万元，用户输入“净利润不少于 5 亿” → 5 亿 ÷ 1 万 = 5 万 →
{'NET_PROFIT': ('>=', 5e4)}

异常处理

未知字段：抛 ValueError 并列出可选 FieldCnName

单位不支持：提示用户已支持单位列表

CSV 标准列
FieldName | FieldCnName | FieldEnName | FieldType | Define | Unit

2 · 条件翻译规则 Condition Translation Rules
自然语言示例	生成的 additional_conditions
总资产大于 100 亿	{'TOTAL_ASSETS': ('>', 1e10)}
净利润不小于 5 亿	{'NET_PROFIT': ('>=', 5e8)}
名称包含“平安”	{'NAME': ('LIKE', '%平安%')}
市值 100–500 亿	{'MARKET_CAP': [1e10, 5e10]}
代码为上证或深证成指	{'CODE': ['SH000001','SZ399001']}
同时满足 A 且 B	{'AND':[condA, condB]}
满足 A 或 B	{'OR':[condA, condB]}

操作符：> < >= <= = != LIKE REGEXP IS NULL IS NOT NULL

默认金额单位：若用户未指明 → 优先查字段 Unit；若无 → 默认“元”。


3. 频率处理规则 Frequency Rules
当用户指定频率（如“按月回测” / “以周频选股”）：

获取交易日序列
cal = get_db_data('AShareCalendar',
                  keywords=['TRADE_DAYS'],
                  additional_conditions={
                      'S_INFO_EXCHMARKET': 'SSE',          # 仅沪市
                      'TRADE_DAYS': ('>=', start_date),    # 可根据上下文确定起止
                  })
按频率取周期最后一日

freq_map = {'日':'D','周':'W','月':'M','季':'Q','年':'A'}

period_ends = cal['TRADE_DAYS'].resample(freq_map[freq], on='TRADE_DAYS').last().dropna()

限制行情查询日期
使用 additional_conditions={'TRADE_DATE': period_ends.tolist()} 调用行情/价格类数据表。

4. 行情数据缓存规则 (Cache Rules)
文件命名
"{asset_cnt}_{freq}_{condkeys}_{savedate}.pkl"
asset_cnt  : 资产/列数量

freq       : D / W / M / Q / A（若未指定频率则为 D）

condkeys   : additional_conditions 所有一级键名按字典序连接，用 “–” 分隔
示例：MARKET_CAP–NET_PROFIT–TRADE_DATE

savedate   : 当天日期，如 20250705

流程

构造完整文件名

本地存在 → 直接 read_pickle

不存在 → get_db_data 抓取 → to_pickle 保存

5 · 任务触发规则 Action Trigger Rules
关键词	系统行为
筛选 / 查询 / 选股 …	调 get_db_data → 返回结果
回测 / backtest / 测试策略 …	先 get_db_data → 构造持仓 → strategy_analyzer(df_positions)

6· 处理模板（伪代码）
def handle_user_query(user_query: str):
    # 1) 解析自然语言（由 Cursor 内置大模型完成）
    table, fields, conds, need_backtest = cursor_model_parse(user_query)

    # 2) 频率 → 交易日过滤
    if freq:
        cal = get_db_data('AShareCalendar',
                          keywords=['TRADE_DAYS'],
                          additional_conditions={'S_INFO_EXCHMARKET': 'SSE'})
        period_ends = (
            cal['TRADE_DAYS']
            .resample({'D':'D','W':'W','M':'M','Q':'Q','A':'A'}[freq],
                      on='TRADE_DAYS').last().dropna()
        )
        conds = conds or {}
        conds['TRADE_DATE'] = period_ends.tolist()

     # 3) 行情缓存
    cache_name = (
        f"{len(fields or [])}_{freq or 'D'}_"
        f"{'-'.join(sorted(conds)) if conds else 'ALL'}_"
        f"{pd.Timestamp.today().strftime('%Y%m%d')}.pkl"
    )
    if os.path.exists(cache_name):
        df = pd.read_pickle(cache_name)
    else:
        进行第(4)步
        df.to_pickle(cache_name)


    # 4) 查询
    df = get_db_data(
        table_name            = table,
        keywords              = fields,          # None → SELECT *
        additional_conditions = conds
    )

    if not need_backtest:
        return preview(df)      # 仅查询

    # 5) 生成持仓（等权示例）
    positions = (
        df.groupby('TRADE_DATE')['CODE']
          .apply(lambda codes: {c: 1/len(codes) for c in codes})
          .pipe(lambda s: pd.DataFrame(s.tolist(), index=s.index))
    )

    # 6) 单参数回测
    perf = strategy_analyzer(positions)
    return perf.summary()

7 · 使用须知
字段 / 表名必须完全匹配 → 均来自 CSV→FieldName

日期列统一 → 默认 TRADE_DATE / REPORT_DATE；歧义时须确认

持仓矩阵格式

index   → 日期 (datetime)
columns → 股票代码
values  → 权重 (float)
错误处理

未找到表/字段 → ValueError 并列候选

条件无法解析 → 提示格式示例


strategy_analyzer 仅需 df_positions 单参数；

确保每个日期‑股票的权重已就绪即可。