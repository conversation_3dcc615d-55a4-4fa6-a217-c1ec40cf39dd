﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_EVENT_CATEGORYCODE,事件类型代码,Event type code,"NUMBER(9,0)",万得自定义的用来区分事件类型的编码；事件类型编码，对应f3_0003，204开头；,
S_EVENT_ANNCEDATE,披露日期,Date of disclosure,VARCHAR2(8),事件发布当天的日期；事件披露的公告日期；,
S_EVENT_HAPDATE,发生日期,Date of occurrence,VARCHAR2(8),公告中披露的事件开始的日期；事件发生的日期；,
S_EVENT_EXPDATE,失效日期,Expiration date,VARCHAR2(8),公告中披露的事件结束的日期；事件失效的日期；,
S_EVENT_CONTENT,事件内容,Event content,VARCHAR2(7000),事件的具体内容说明；内部字段，存储事件内容；,
S_EVENT_TEMPLATEID,[废弃]模板ID,[Discarded]Template ID,"NUMBER(12,0)",万得自定义用来区分同一公司同一时间发生的同一事件类型，由事件类型代码加三位序号组成；内部模板ID，事件类型代码+3位序号；,
SEC_ID,证券ID,Securities ID,VARCHAR2(10),万得定义的内部代码，用于标识证券唯一性,
TEMPLATE1,事件模板,TEMPLATE,VARCHAR2(800),关联大事提醒中的事件内容说明，以模板ID字段关联,
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(10),万得定义的内部代码，用于标识公司唯一性,
