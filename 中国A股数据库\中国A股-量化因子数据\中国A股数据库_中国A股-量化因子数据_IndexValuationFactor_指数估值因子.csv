﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object id,VARCHAR2(100),主键,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),股票指数交易代码,
TRADE_DT,交易日期,Date of transaction,VARCHAR2(8),指数的交易日期,
INDEX_NUMBER,成份股个数,Number of constituent stocks,"NUMBER(20,4)",剔除停牌股票后的指数成份数量,个
I_TECH_UPPCT,上涨的股票占指数成份股的比例,The proportion of rising stocks to index stocks,"NUMBER(20,4)","上涨的股票占指数成份股的比例；
上涨股票定义为涨跌幅>0的成分股。
所属【指数因子】",%
I_TECH_DOWNPCT,下跌的股票占指数成份股的比例,The proportion of declining stocks to index stocks,"NUMBER(20,4)","下跌的股票占指数成份股的比例；
下跌股票定义为涨跌幅<0的成分股。
所属【指数因子】",%
I_TECH_LIMITUPPCT,涨停的股票占指数成份股的比例,The proportion of fluctuating stocks to index stocks,"NUMBER(20,4)","涨停的股票占指数成份股的比例；
涨停股票定义：成分股中股票状态=涨停状态。
所属【指数因子】",%
I_TECH_LIMITDOWNPCT,跌停的股票占指数成份股的比例,Falling stocks account for the proportion of index stocks,"NUMBER(20,4)","跌停的股票占指数成份股的比例；
跌停股票定义：成分股中股票状态=跌停状态。
所属【指数因子】",%
I_VAL_PE2,算术平均滚动市盈率,Arithmetic average rolling pe ratio,"NUMBER(20,4)","计算方法：计算每个指数成份股的的PE，去除小于0和大于1000的值，然后做简单平均。
所属【指数因子】",倍
I_VAL_PB2,算术平均滚动市净率,Arithmetic average rolling market net rate,"NUMBER(20,4)","计算方法：计算每个指数成份股的的PB，去除小于0和大于1000的值，然后做简单平均。
所属【指数因子】",倍
I_VAL_PE1,加权市盈率,Weighted pe ratio,"NUMBER(20,4)","计算指数成份股的归属母公司所有者权益合计的累加值/计算指数成份股的净利润的累加值,得到PE",倍
I_VAL_PB1,加权市净率,Weighted market-to-net ratio,"NUMBER(20,4)","计算指数成份股的归属母公司所有者权益合计的累加值/计算指数成份股的净资产的累加值,得到PB",倍
I_VAL_PE3,PE(中位数),Pe (median),"NUMBER(20,4)",指数成份股市盈率中位数,倍
I_VAL_PB3,PB(中位数),Pb (median),"NUMBER(20,4)",指数成份股市净率中位数,倍
I_VAL_PE_TTM_EX_NV,"PE(TTM,剔除负值整体法)","PE (TTM, Exclude Negative Value)","NUMBER(20,4)","sum(成份股,总市值2)/sum(成份股,归母净利润(TTM)) ,归母净利润(TTM)为负的成份不参与计算",倍
I_DIV_EFFECT,分红影响,Dividend Effect,"NUMBER(20,4)",所有成份分红对指数点位影响的加总,
