#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试优化后的统一数据处理版本（不使用apply）
"""

import pandas as pd
import numpy as np
import time
from pathlib import Path
from convertible_bond_strategy_unified import UnifiedDataProcessor

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 创建交易日期
    dates = pd.date_range('2024-01-01', '2024-01-31', freq='D')
    trading_dates = dates[dates.weekday < 5]  # 只保留工作日
    
    # 创建可转债和正股代码
    bond_codes = [f'bond_{i:03d}' for i in range(1, 21)]  # 20只可转债
    stock_codes = [f'stock_{i:03d}' for i in range(1, 21)]  # 20只正股
    
    # 创建模拟数据
    data_folder = Path('TestData')
    data_folder.mkdir(exist_ok=True)
    
    processor = UnifiedDataProcessor('2024-01-01', '2024-01-31', data_folder)
    
    # 1. 交易日历
    trading_dates_df = pd.DataFrame({'TRADE_DT': trading_dates.strftime('%Y%m%d')})
    processor.save_data(trading_dates_df, 'trading_dates')
    
    # 2. 可转债估值数据
    bond_valuation_data = []
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for bond_code in bond_codes:
            bond_valuation_data.append({
                'S_INFO_WINDCODE': bond_code,
                'TRADE_DT': date_str,
                'CB_ANAL_YTM': np.random.uniform(0.01, 0.08),
                'CB_ANAL_PTM': np.random.uniform(0.5, 3.0),
                'CB_ANAL_CONVPREMIUMRATIO': np.random.uniform(0.05, 0.30)
            })
    
    bond_valuation_df = pd.DataFrame(bond_valuation_data)
    processor.save_data(bond_valuation_df, 'bond_valuation')
    
    # 3. 可转债余额数据（每只债券多条记录，模拟历史变化）
    bond_balance_data = []
    for bond_code in bond_codes:
        # 每只债券有2-3条历史记录
        for i, date in enumerate(['20231201', '20240101', '20240115']):
            if i < np.random.randint(2, 4):  # 随机生成2-3条记录
                bond_balance_data.append({
                    'S_INFO_WINDCODE': bond_code,
                    'S_INFO_CHANGEDATE': date,
                    'B_INFO_OUTSTANDINGBALANCE': np.random.uniform(5, 20)
                })
    
    bond_balance_df = pd.DataFrame(bond_balance_data)
    processor.save_data(bond_balance_df, 'bond_balance')
    
    # 4. 可转债评级数据
    ratings = ['AAA', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-']
    bond_rating_data = []
    for bond_code in bond_codes:
        # 每只债券有1-2条评级记录
        for i, date in enumerate(['20231201', '20240101']):
            if i < np.random.randint(1, 3):
                bond_rating_data.append({
                    'S_INFO_WINDCODE': bond_code,
                    'ANN_DT': date,
                    'B_INFO_CREDITRATING': np.random.choice(ratings)
                })
    
    bond_rating_df = pd.DataFrame(bond_rating_data)
    processor.save_data(bond_rating_df, 'bond_rating')
    
    # 5. 可转债-正股映射
    mapping_data = pd.DataFrame({
        'S_INFO_WINDCODE': bond_codes,
        'S_INFO_UNDERLYINGWINDCODE': stock_codes
    })
    processor.save_data(mapping_data, 'bond_stock_mapping')
    
    # 6. 正股价格数据
    stock_price_data = []
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for stock_code in stock_codes:
            stock_price_data.append({
                'S_INFO_WINDCODE': stock_code,
                'TRADE_DT': date_str,
                'S_DQ_CLOSE': np.random.uniform(3, 50),
                'S_DQ_AMOUNT': np.random.uniform(5000000, 50000000)
            })
    
    stock_price_df = pd.DataFrame(stock_price_data)
    processor.save_data(stock_price_df, 'stock_prices')
    
    # 7. 正股市值数据
    stock_market_cap_data = []
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for stock_code in stock_codes:
            stock_market_cap_data.append({
                'S_INFO_WINDCODE': stock_code,
                'TRADE_DT': date_str,
                'S_VAL_MV': np.random.uniform(20, 200)
            })
    
    stock_market_cap_df = pd.DataFrame(stock_market_cap_data)
    processor.save_data(stock_market_cap_df, 'stock_market_cap')
    
    # 8. 正股财务数据
    stock_financial_data = []
    for stock_code in stock_codes:
        # 每只股票有1-2条财务记录
        for i, date in enumerate(['20231201', '20240101']):
            if i < np.random.randint(1, 3):
                stock_financial_data.append({
                    'S_INFO_WINDCODE': stock_code,
                    'ANN_DT': date,
                    'NET_PROFIT_EXCL_MIN_INT_INC': np.random.uniform(-1000000, 5000000)
                })
    
    stock_financial_df = pd.DataFrame(stock_financial_data)
    processor.save_data(stock_financial_df, 'stock_financial')
    
    # 9. 可转债价格数据
    bond_price_data = []
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for bond_code in bond_codes:
            bond_price_data.append({
                'S_INFO_WINDCODE': bond_code,
                'TRADE_DT': date_str,
                'S_DQ_CLOSE': np.random.uniform(90, 150)
            })
    
    bond_price_df = pd.DataFrame(bond_price_data)
    processor.save_data(bond_price_df, 'bond_prices')
    
    print(f"测试数据创建完成:")
    print(f"  - 交易日期: {len(trading_dates)} 天")
    print(f"  - 可转债数量: {len(bond_codes)} 只")
    print(f"  - 估值数据: {len(bond_valuation_df)} 条")
    print(f"  - 余额数据: {len(bond_balance_df)} 条")
    print(f"  - 评级数据: {len(bond_rating_df)} 条")
    print(f"  - 财务数据: {len(stock_financial_df)} 条")
    
    return processor

def test_optimized_processing():
    """测试优化后的数据处理"""
    print("=" * 60)
    print("测试优化后的数据处理（不使用apply）")
    print("=" * 60)
    
    # 创建测试数据
    processor = create_test_data()
    
    # 测试统一数据集创建
    print("\n开始测试统一数据集创建...")
    start_time = time.time()
    
    success = processor.create_unified_dataset()
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    if success:
        print(f"✅ 统一数据集创建成功")
        print(f"⏱️ 处理时间: {processing_time:.3f}秒")
        print(f"📊 数据集大小: {len(processor.unified_dataset)} 条记录")
        print(f"📋 数据字段: {list(processor.unified_dataset.columns)}")
        
        # 检查数据质量
        print(f"\n数据质量检查:")
        for col in processor.unified_dataset.columns:
            null_count = processor.unified_dataset[col].isnull().sum()
            null_pct = null_count / len(processor.unified_dataset) * 100
            print(f"  - {col}: {null_count} 个缺失值 ({null_pct:.1f}%)")
        
        # 验证时间序列数据匹配的正确性
        print(f"\n验证时间序列数据匹配:")
        
        # 检查余额数据
        sample_data = processor.unified_dataset.head(10)
        print(f"  - 余额数据样本: {sample_data['BOND_BALANCE'].notna().sum()}/10 条有效")
        
        # 检查评级数据
        print(f"  - 评级数据样本: {sample_data['BOND_RATING'].notna().sum()}/10 条有效")
        
        # 检查财务数据
        print(f"  - 财务数据样本: {sample_data['STOCK_NET_PROFIT'].notna().sum()}/10 条有效")
        
        return True
    else:
        print("❌ 统一数据集创建失败")
        return False

def test_performance_comparison():
    """测试性能对比（模拟apply vs merge_asof）"""
    print("\n" + "=" * 60)
    print("性能对比测试（模拟apply vs merge_asof）")
    print("=" * 60)
    
    # 创建更大的测试数据集
    n_bonds = 100
    n_days = 60
    
    # 创建主数据
    dates = pd.date_range('2024-01-01', periods=n_days, freq='D')
    bonds = [f'bond_{i:03d}' for i in range(n_bonds)]
    
    main_data = []
    for date in dates:
        for bond in bonds:
            main_data.append({
                'bond_code': bond,
                'trade_date': date,
                'value': np.random.random()
            })
    
    main_df = pd.DataFrame(main_data)
    
    # 创建时间序列数据（余额变化）
    balance_data = []
    for bond in bonds:
        # 每只债券有3-5次余额变化
        change_dates = pd.date_range('2023-12-01', '2024-02-29', freq='15D')
        for change_date in change_dates[:np.random.randint(3, 6)]:
            balance_data.append({
                'bond_code': bond,
                'change_date': change_date,
                'balance': np.random.uniform(5, 20)
            })
    
    balance_df = pd.DataFrame(balance_data)
    
    print(f"测试数据规模:")
    print(f"  - 主数据: {len(main_df)} 条")
    print(f"  - 余额数据: {len(balance_df)} 条")
    
    # 方法1：使用apply（模拟原来的方式）
    print(f"\n方法1：使用apply匹配...")
    start_time = time.time()
    
    def get_latest_balance_apply(row):
        bond_code = row['bond_code']
        trade_date = row['trade_date']
        
        subset = balance_df[
            (balance_df['bond_code'] == bond_code) &
            (balance_df['change_date'] <= trade_date)
        ]
        
        if not subset.empty:
            return subset.loc[subset['change_date'].idxmax(), 'balance']
        else:
            return np.nan
    
    result_apply = main_df.copy()
    result_apply['balance'] = main_df.apply(get_latest_balance_apply, axis=1)
    
    apply_time = time.time() - start_time
    
    # 方法2：使用向量化merge（优化后的方式）
    print(f"方法2：使用向量化merge匹配...")
    start_time = time.time()

    # 创建映射表（类似我们在主代码中的做法）
    mapping_data = []
    bonds = main_df['bond_code'].unique()
    dates = main_df['trade_date'].unique()

    for bond in bonds:
        bond_balance_subset = balance_df[balance_df['bond_code'] == bond]
        if not bond_balance_subset.empty:
            bond_balance_subset = bond_balance_subset.sort_values('change_date')

            for date in dates:
                valid_balance = bond_balance_subset[
                    bond_balance_subset['change_date'] <= date
                ]
                if not valid_balance.empty:
                    latest_balance = valid_balance.iloc[-1]['balance']
                    mapping_data.append({
                        'bond_code': bond,
                        'trade_date': date,
                        'balance': latest_balance
                    })

    if mapping_data:
        mapping_df = pd.DataFrame(mapping_data)
        result_vectorized = main_df.merge(
            mapping_df[['bond_code', 'trade_date', 'balance']],
            on=['bond_code', 'trade_date'], how='left'
        )
    else:
        result_vectorized = main_df.copy()
        result_vectorized['balance'] = np.nan

    vectorized_time = time.time() - start_time
    
    # 性能对比
    print(f"\n性能对比结果:")
    print(f"  - apply方法耗时: {apply_time:.3f}秒")
    print(f"  - 向量化方法耗时: {vectorized_time:.3f}秒")

    speedup = apply_time / vectorized_time
    print(f"  - 性能提升: {speedup:.1f}x")
    print(f"  - 时间节省: {(1 - vectorized_time/apply_time)*100:.1f}%")

    # 验证结果一致性
    print(f"\n结果验证:")
    print(f"  - apply结果有效数据: {result_apply['balance'].notna().sum()} 条")
    print(f"  - 向量化结果有效数据: {result_vectorized['balance'].notna().sum()} 条")

    if speedup > 1:
        print(f"  - ✅ 向量化方法性能更优")
    else:
        print(f"  - ⚠️ apply方法性能更优")

def main():
    """主测试函数"""
    try:
        # 测试优化后的数据处理
        success = test_optimized_processing()
        
        if success:
            # 测试性能对比
            test_performance_comparison()
            
            print("\n" + "=" * 80)
            print("🎉 优化测试全部完成！")
            print("✅ 已移除所有apply操作，使用高效的pandas操作")
            print("✅ 使用merge_asof进行时间序列数据匹配")
            print("✅ 显著提升数据处理性能")
            print("=" * 80)
        else:
            print("\n❌ 优化测试失败")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
