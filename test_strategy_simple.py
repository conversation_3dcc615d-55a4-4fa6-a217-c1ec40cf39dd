#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版可转债策略测试
"""

import pandas as pd
import numpy as np
from datetime import datetime
from convertible_bond_strategy import ConvertibleBondStrategy

def test_single_month_strategy():
    """测试单月策略运行"""
    print("=" * 60)
    print("测试单月可转债策略")
    print("=" * 60)
    
    # 创建策略实例 - 只测试2024年1月
    strategy = ConvertibleBondStrategy(start_date='2024-01-01', end_date='2024-01-31')
    
    # 获取交易日历
    trade_dates = strategy.get_trading_dates()
    if not trade_dates:
        print("获取交易日历失败")
        return
    
    print(f"获取到 {len(trade_dates)} 个交易日")
    
    # 获取月末日期
    month_ends = strategy.get_month_end_dates(trade_dates)
    if not month_ends:
        print("获取月末日期失败")
        return
    
    print(f"月末调仓日期: {month_ends}")
    
    # 测试单个日期的债券选择
    test_date = month_ends[0]  # 使用第一个月末日期
    print(f"\n测试日期: {test_date}")
    
    try:
        # 获取可转债列表
        bond_codes = strategy.get_convertible_bonds_list()
        print(f"获取到 {len(bond_codes)} 只可转债")
        
        # 限制数量以加快测试
        bond_codes = bond_codes[:50]  # 只测试前50只
        print(f"测试前 {len(bond_codes)} 只可转债")
        
        # 获取可转债数据
        bond_data = strategy.get_convertible_bond_data(bond_codes, test_date)
        if bond_data.empty:
            print("未获取到可转债数据")
            return
        
        print(f"获取到 {len(bond_data)} 条可转债数据")
        print("可转债数据示例:")
        print(bond_data[['S_INFO_WINDCODE', 'CB_ANAL_YTM', 'CB_ANAL_PTM']].head())
        
        # 获取正股代码映射
        bond_stock_mapping = strategy.get_bond_stock_mapping(bond_data['S_INFO_WINDCODE'].tolist())
        if not bond_stock_mapping:
            print("使用简化映射")
            bond_stock_mapping = {bond: strategy.extract_stock_code(bond) for bond in bond_data['S_INFO_WINDCODE'].tolist()}
        
        # 添加正股代码
        bond_data['STOCK_CODE'] = bond_data['S_INFO_WINDCODE'].map(bond_stock_mapping)
        bond_data = bond_data.dropna(subset=['STOCK_CODE'])
        
        if bond_data.empty:
            print("映射后无有效数据")
            return
        
        print(f"映射后剩余 {len(bond_data)} 条数据")
        
        # 获取正股数据
        stock_codes = bond_data['STOCK_CODE'].unique().tolist()
        print(f"需要获取 {len(stock_codes)} 只正股数据")
        
        stock_data = strategy.get_stock_data(stock_codes, test_date, trade_dates)
        if not stock_data.empty:
            print(f"获取到 {len(stock_data)} 条正股价格数据")
        
        financial_data = strategy.get_stock_financial_data(stock_codes, test_date)
        if not financial_data.empty:
            print(f"获取到 {len(financial_data)} 条财务数据")
        
        volume_data = strategy.get_stock_volume_data(stock_codes, test_date, trade_dates)
        if not volume_data.empty:
            print(f"获取到 {len(volume_data)} 条成交额数据")
        
        # 合并数据
        merged_data = bond_data.copy()
        
        if not stock_data.empty:
            stock_data_renamed = stock_data.rename(columns={'S_INFO_WINDCODE': 'STOCK_CODE'})
            merged_data = pd.merge(merged_data, stock_data_renamed, on='STOCK_CODE', how='left')
            print("已合并正股价格数据")
        
        if not financial_data.empty:
            financial_data_renamed = financial_data.rename(columns={'S_INFO_WINDCODE': 'STOCK_CODE'})
            merged_data = pd.merge(merged_data, financial_data_renamed, on='STOCK_CODE', how='left')
            print("已合并财务数据")
        
        if not volume_data.empty:
            volume_data_renamed = volume_data.rename(columns={'S_INFO_WINDCODE': 'STOCK_CODE'})
            merged_data = pd.merge(merged_data, volume_data_renamed, on='STOCK_CODE', how='left')
            print("已合并成交额数据")
        
        print(f"最终合并数据: {len(merged_data)} 条")
        print("合并数据列名:", merged_data.columns.tolist())
        
        # 应用筛选条件
        print("\n开始应用筛选条件...")
        filtered_data = strategy.apply_screening_conditions(merged_data)
        
        if not filtered_data.empty:
            print(f"\n筛选结果: {len(filtered_data)} 只可转债符合条件")
            print("符合条件的可转债:")
            result_cols = ['S_INFO_WINDCODE', 'CB_ANAL_YTM', 'CB_ANAL_PTM']
            if 'S_DQ_CLOSE' in filtered_data.columns:
                result_cols.append('S_DQ_CLOSE')
            if 'B_INFO_OUTSTANDINGBALANCE' in filtered_data.columns:
                result_cols.append('B_INFO_OUTSTANDINGBALANCE')
            
            available_cols = [col for col in result_cols if col in filtered_data.columns]
            print(filtered_data[available_cols].head(10))
        else:
            print("未找到符合条件的可转债")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    test_single_month_strategy()

if __name__ == "__main__":
    main()
