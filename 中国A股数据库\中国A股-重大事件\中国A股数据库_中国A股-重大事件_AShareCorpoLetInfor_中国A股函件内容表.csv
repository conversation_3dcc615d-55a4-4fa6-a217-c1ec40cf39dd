﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,object id,VARCHAR2(38),,
S_INFO_WINDCODE,Wind代码,Windcode,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
COMP_ID,公司ID,Company id,VARCHAR2(10),万得自定义的用来识别公司的唯一编码,
PROB_ID,问题ID,Problem id,"NUMBER(11,0)",万得自定义的用来识别函件中每一条问题的唯一编码,
LETTERS_EVENT_ID,函件事件ID,Letter_event_id,VARCHAR2(20),万得自定义函件的编码,
NUMBER1,序号,Number,"NUMBER(20,0)",根据函件公告展示问题顺序的编号,
PROB_TYPE,问题类型代码,Problem type code,VARCHAR2(1024),根据函件问题中的关键字，自动识别并匹配,
PROB_TXT,问题文本,Question text,CLOB,,
ANSW_TXT,回答文本,Answer text,CLOB,,
SUP_ANSW_TXT,补充回答文本,Supplementary answer text,CLOB,,
IS_VALID,是否有效,Is it valid,"NUMBER(1,0)",是否有效：1是0否,
