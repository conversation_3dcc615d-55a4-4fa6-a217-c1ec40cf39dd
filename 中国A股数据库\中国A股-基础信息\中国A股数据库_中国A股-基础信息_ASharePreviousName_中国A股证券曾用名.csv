﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(38),业务主键：S_INFO_WINDCODE+BEGINDATE,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
BEGINDATE,起始日期,Start date,VARCHAR2(8),记录证券简称的开始日期；开始使用当前证券简称的日期；记录变动前万得代码对应证券的上市日期；,
ENDDATE,截至日期,Deadline,VARCHAR2(8),记录证券简称的开始日期；开始使用当前证券简称的日期；记录变动前万得代码对应证券的上市日期；,
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期；基金公司发布当前证券简称公告的日期；公告发布日期或记录新增的系统日期；,
S_INFO_NAME,证券简称,Securities short name,VARCHAR2(100),由证券交易所或证券登记结算机构根据一定规则制定的，用于简化公司名称并方便证券交易的缩写；当前证券使用的简称；记录交易所公布证券变动前的中文简称，若未公布则为万得自编简称；,
CHANGEREASON,变动原因代码,Change reason code,"NUMBER(9,0)",字段枚举值请见常见问题Q1,
SEC_EXPANSION_ABBREVIATION,证券扩位简称,Securities expansion abbreviation,VARCHAR2(500),交易所披露的扩位证券简称,
S_INFO_ASHARECODE,证券ID,Securities ID,VARCHAR2(10),万得自定义的用来识别证券的唯一编码,
