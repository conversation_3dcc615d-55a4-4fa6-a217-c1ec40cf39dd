#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行可转债量化策略的主程序
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from convertible_bond_strategy import ConvertibleBondStrategy

def run_short_term_test():
    """运行短期测试（3个月）"""
    print("=" * 60)
    print("运行可转债策略短期测试（2024年1-3月）")
    print("=" * 60)
    
    # 创建策略实例 - 测试3个月
    strategy = ConvertibleBondStrategy(start_date='2024-01-01', end_date='2024-03-31')
    
    try:
        # 运行回测
        strategy.run_backtest()
        
    except Exception as e:
        print(f"短期测试失败: {e}")
        import traceback
        traceback.print_exc()

def run_medium_term_test():
    """运行中期测试（1年）"""
    print("=" * 60)
    print("运行可转债策略中期测试（2024年全年）")
    print("=" * 60)
    
    # 创建策略实例 - 测试1年
    strategy = ConvertibleBondStrategy(start_date='2024-01-01', end_date='2024-12-31')
    
    try:
        # 运行回测
        strategy.run_backtest()
        
    except Exception as e:
        print(f"中期测试失败: {e}")
        import traceback
        traceback.print_exc()

def run_full_backtest():
    """运行完整回测（5年）"""
    print("=" * 60)
    print("运行可转债策略完整回测（2020-2024年）")
    print("=" * 60)
    
    # 创建策略实例 - 完整回测
    strategy = ConvertibleBondStrategy(start_date='2020-01-01', end_date='2024-12-31')
    
    try:
        # 运行回测
        strategy.run_backtest()
        
    except Exception as e:
        print(f"完整回测失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("可转债量化策略回测程序")
    print("=" * 60)
    
    while True:
        print("\n请选择测试模式:")
        print("1. 短期测试（2024年1-3月，3个月）")
        print("2. 中期测试（2024年全年，12个月）")
        print("3. 完整回测（2020-2024年，5年）")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            run_short_term_test()
        elif choice == '2':
            run_medium_term_test()
        elif choice == '3':
            run_full_backtest()
        elif choice == '4':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
