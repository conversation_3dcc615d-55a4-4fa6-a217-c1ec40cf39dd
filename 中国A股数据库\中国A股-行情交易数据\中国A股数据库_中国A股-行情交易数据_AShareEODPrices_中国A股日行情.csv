﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
TRADE_DT,交易日期,transaction date,VARCHAR2(8),,
CRNCY_CODE,货币代码,Currency code,VARCHAR2(10),证券用于结算的交易币种；在商品市场用于交易的货币种类；该证券交易采用的币种；在外汇市场或货币市场上用于交易的货币种类；,
S_DQ_PRECLOSE,昨收盘价(元),Yesterday's closing price (yuan),"NUMBER(20,4)",昨收指的是该股票昨天的收盘价，昨天指的是上一个交易日；当前基金在交易所前一天收盘价；前一交易日的收盘价格，回购的量纲为%，指数无量纲；,元
S_DQ_OPEN,开盘价(元),Opening price (yuan),"NUMBER(20,4)",是当日股票交易价格的第一个价格，如果当日集合竞价没有成交，就以上一日的收盘价为当日的开盘价；当前基金在交易所当天的开盘价；交易当天的开盘成交价格，回购的量纲为%，指数无量纲；,元
S_DQ_HIGH,最高价(元),Highest price (yuan),"NUMBER(20,4)",最高价是指个股成交的最高价位，是指某种证券在每个交易日成交的最高价格；当前基金在交易所当天的最高价；交易当天的最高成交价格，回购的量纲为%，指数无量纲；,元
S_DQ_LOW,最低价(元),Lowest price (yuan),"NUMBER(20,4)",最低价是指个股成交的最低价位，是指某支个股在每个交易日，从开盘到收盘的过程中，成交的最低价格；当前基金在交易所当天的最低价；交易当天的最低成交价格，回购的量纲为%，指数无量纲；,元
S_DQ_CLOSE,收盘价(元),Closing price (yuan),"NUMBER(20,4)",当日股票交易价格的最后一个价格，如果收盘集中竞价没有价格，就以上一笔价格为当日收盘价格；当前基金在交易所当天的收盘价；交易当天的收盘成交价格，回购的量纲为%，指数无量纲；,元
S_DQ_CHANGE,涨跌(元),Ups and downs (yuan),"NUMBER(20,4)",S_DQ_CLOSE-S_DQ_PRECLOSE,
S_DQ_PCTCHANGE,涨跌幅(%),Quote change(%),"NUMBER(20,4)","ROUND((S_DQ_CLOSE - S_DQ_PRECLOSE) * 100 / S_DQ_PRECLOSE, 2)",
S_DQ_VOLUME,成交量(手),Volume (hand),"NUMBER(20,4)",,手
S_DQ_AMOUNT,成交金额(千元),Transaction amount (1000 yuan),"NUMBER(20,4)",,千元
S_DQ_ADJPRECLOSE,复权昨收盘价(元),Resumption of power yesterday closing price (yuan),"NUMBER(20,4)",复权昨收盘价；当前基金前一天进行权息修复的后收盘价；,元
S_DQ_ADJOPEN,复权开盘价(元),Re-opening price (yuan),"NUMBER(20,4)",复权开盘价；当前基金在交易所进行权息修复的后当天的开盘价；,元
S_DQ_ADJHIGH,复权最高价(元),The highest price of reinstatement (yuan),"NUMBER(20,4)",复权最高价；当前基金在交易所进行权息修复的后当天的最高价；,元
S_DQ_ADJLOW,复权最低价(元),Minimum right of reinstatement (yuan),"NUMBER(20,4)",复权最低价；当前基金在交易所进行权息修复的后当天的最低价；,元
S_DQ_ADJCLOSE,复权收盘价(元),Resumption closing price (yuan),"NUMBER(20,4)",复权收盘价；当前基金在交易所进行权息修复的后当天的收盘价；,元
S_DQ_ADJFACTOR,复权因子,Complex factor,"NUMBER(20,6)",当日复权因子=前一交易日收盘价/当日昨收盘价*前一交易日复权因子，股票上市当天初始值为1。,
S_DQ_AVGPRICE,均价(VWAP),Average price (VWAP),"NUMBER(20,4)",成交金额/成交量,
S_DQ_TRADESTATUS,交易状态,trading status,VARCHAR2(10),附注 XD:除息 XR:除权 DR:除权除息 N:上市首日,
S_DQ_TRADESTATUSCODE,交易状态代码,Trading status code,"NUMBER(5,0)",记录交易时的状态：正常、停牌、除权等信息；该条记录的交易状态：正常、停牌、除权等；,
S_DQ_LIMIT,涨停价(元),Limit price(yuan),"NUMBER(20,4)",记录该只股票在证券交易总当涨幅达到规定时，便不可以继续上涨，这时的价格是涨停价,元
S_DQ_STOPPING,跌停价(元),Stopping price(yuan),"NUMBER(20,4)",记录该只股票在证券交易中当跌幅达到规定时，便不可以继续下跌，这时的价格是跌停价,元
S_DQ_ADJCLOSE_BACKWARD,前复权收盘价(元),Backward-adjusted Closing Price (yuan),"NUMBER(20,4)",,
