﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_WINDCODE,Wind代码,Wind Code,VARCHAR2(40),信息披露方,
S_INFO_COMPCODE,公司ID,Company Code,VARCHAR2(40),Wind自编代码，公司唯一性,
RELATION,担保方与披露方关系,Relationship between the guarantor and the discloser,VARCHAR2(40),单次担保中给他人提供担保的公司与公司之间的关系,
GUARANTOR,担保方公司名称,The name of the guarantor,VARCHAR2(100),提供担保方、担保人,
RELATION2,被担保方与披露方关系,The relationship between the Guaranteed company and the discloser,VARCHAR2(40),单次担保中需他人提供担保的公司与公司之间的关系,
SECUREDPARTY,被担保方公司名称,The name of the company to be guaranteed,VARCHAR2(100),单次担保中需他人提供担保的公司,
METHOD,担保方式,Guarantee method,VARCHAR2(40),附注,
AMOUNT,担保金额(元),Guaranteed amount,"NUMBER(20,4)",公司提供担保总金额,万元
CRNCY_CODE,币种,Currency code,VARCHAR2(10),在外汇市场或货币市场上用于交易的货币种类,
TERM,担保期限(年),Warranty period (years),"NUMBER(20,4)",单次提供担保的年限,年
START_DT,担保起始日期,The starting date of the guarantee,VARCHAR2(8),单次提供担保的开始时间,
END_DT,担保终止日期,The end date of the guarantee,VARCHAR2(8),单次提供担保的结束时间,
IS_COMPLETE,是否履行完毕,Fulfilled,"NUMBER(1,0)",单次担保是否已履行完毕,
IS_RELATED,是否关联交易,Related transactions,"NUMBER(1,0)",单次担保是否构成公司与关联方交易,
TRADE_DT,交易日期,Trade date,VARCHAR2(8),公司担保实际发生变动的日期,
REPORT_PERIOD,报告期,report period,VARCHAR2(8),单次担保公布完成的时间,
IS_OVERDUE,担保是否逾期,Overdue,"NUMBER(1,0)",公司与交易对方之间的担保是否存在逾期的部分,
OVERDUE_AMOUNT,担保逾期金额(元),Overdue amount,"NUMBER(20,4)",公司存在与交易对方之间的担保已逾期的部分,万元
IS_COUNTERGUARANTEE,是否存在反担保,Mutual guarantee,"NUMBER(1,0)",单次提供担保是否存在其他公司提供反担保情况,
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期,
SEQUENCE,序号,Serial number,VARCHAR2(4),标识担保事件唯一性的编号。对于同一担保方和被担保方在担保截止日相同时，用序号进行标识，来区分唯一性,
