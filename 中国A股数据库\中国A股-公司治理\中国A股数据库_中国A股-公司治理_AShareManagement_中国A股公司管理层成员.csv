﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_WINDCODE,Wind代码,Wind Code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
ANN_DATE,公告日期,,VARCHAR2(8),记录公告发布当天的日期；公告材料的公开发布的日期；抓取公告当天的日期；有多个阶段的事件，最新披露该事件的日期；公告发布当天的日期；,
S_INFO_MANAGER_NAME,姓名,,VARCHAR2(200),记录上市公司公布的高管姓名；属于该公司的主要人员的姓名；公司职员在个人档案中正在使用的名字；高管的姓氏和名字；,
S_INFO_MANAGER_GENDER,性别代码,,VARCHAR2(10),记录高管的性别代码；人员的性别；通过观察生物学标准来确定一个人的性别；万得自定义的用来识别高管个人性别的编码；,
S_INFO_MANAGER_EDUCATION,学历,,VARCHAR2(20),记录高管的学历；人员的最高学历；一个人接受教育的阶段；高管在教育机构中接受科学、文化知识训练的学习经历；,
S_INFO_MANAGER_NATIONALITY,国籍,,VARCHAR2(40),记录高管的国籍；一个人属于某个国家的一种法律上的身份，表明一个人同一个特定国家间的固定的法律联系；高管具有的属于国家的身份；,
S_INFO_MANAGER_BIRTHYEAR,出生日期,,VARCHAR2(8),记录高管的出生日期；个人档案中最早形成材料记载的出生时间；高管出生当天的日期；,
S_INFO_MANAGER_STARTDATE,任职日期,,VARCHAR2(8),记录高管的任职日期；人员任职职务的日期；公司职员担任某个职务的日期；高管开始担任职务的日期；,
S_INFO_MANAGER_LEAVEDATE,离任日期,,VARCHAR2(8),记录高管的离任日期；人员离职职务的日期；公司职员离任某个职务的日期；高管不再担任职务的日期；,
S_INFO_MANAGER_TYPE,管理层类别,Management category,"NUMBER(5,0)","0：董事会成员
1：高管成员
2：监事会成员
3：委员会成员
4：投资决策委员会
5：核心技术人员
6：境外投资决策委员会
7：其他成员",
S_INFO_MANAGER_POST,公布职务名称,,VARCHAR2(100),记录高管公布的职务名称；公布的人员职务名称；社会组织机构内具有相当数量和重要性梯次的一系列职位的集合或统称；公布的高管在公司内所担任的职务名称；,
S_INFO_MANAGER_INTRODUCTION,个人简历,,VARCHAR2(3000),记录高管的个人简历；公布的人员过往从业经历；公司职员给单位发的一份有针对性的自我介绍；高管简明扼要的个人履历；,
MANID,人物id,,VARCHAR2(10),记录高管统一规范化名称；万得自定义的用来识别人物的唯一编码；万得自定义的用来识别高管个人的唯一编码；,
DISPLAY_ORDER,展示顺序,,"NUMBER(4,0)",记录高管在终端的展示顺序；对于共同职位的展示进行先后排序；在WFT中的展示顺序；,
POSTION_CODE,规范职务代码,,"NUMBER(9,0)",对应f3_0003，109开头,
ISCORE,是否核心技术人员,whether it is a core technician,"NUMBER(1,0)",值为1，则该条记录为核心技术人员,
