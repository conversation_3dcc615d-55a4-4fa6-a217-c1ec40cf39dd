﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(100),记录上市公司在证券市场中公布的交易代码；万得自定义的用来识别公司的唯一编码；,
S_INFO_DIMENSION,一级关系维度名称,Tier 1 relationship dimension name,VARCHAR2(100),,
S_INFO_DIMENSION1,二级关系维度名称,Secondary relationship dimension name,VARCHAR2(100),,
S_INFO_MANAGER_NAME,姓名,Name,VARCHAR2(200),记录上市公司公布的高管姓名；属于该公司的主要人员的姓名；公司职员在个人档案中正在使用的名字；高管的姓氏和名字；,
S_INFO_MANAGER_STARTDATE,任职日期,Date of appointment,VARCHAR2(8),记录高管的任职日期；人员任职职务的日期；公司职员担任某个职务的日期；高管开始担任职务的日期；,
S_INFO_MANAGER_POST,职务,Position,VARCHAR2(100),记录高管公布的职务名称；公布的人员职务名称；社会组织机构内具有相当数量和重要性梯次的一系列职位的集合或统称；公布的高管在公司内所担任的职务名称；,
S_INFO_MANID,人物id,Character Id,VARCHAR2(10),记录高管统一规范化名称；万得自定义的用来识别人物的唯一编码；万得自定义的用来识别高管个人的唯一编码；,
