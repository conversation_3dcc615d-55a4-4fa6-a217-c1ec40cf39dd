﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object id,VARCHAR2(100),主键,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),证券交易代码,
TRADE_DT,交易日期,Date of transaction,VARCHAR2(8),指定交易日期,
s_tech_price1M,当前股价除以过去1个月股价均值再减1,The current share price is divided by the average share price in the past month and is further reduced by 1,"NUMBER(20,8)",当前股价除以过去1个月股价均值再减1。属于超买超卖类因子。,倍
s_tech_price3M,当前股价除以过去3个月股价均值再减1,The current share price divided by the average of the past three months is further reduced by 1,"NUMBER(20,8)",当前股价/过去60日均价-1,倍
s_tech_price1Y,当前股价除以过去1年的股价均值再减1,The current share price divided by the average share price over the past year is further reduced by 1,"NUMBER(20,8)",当前股价/过去250日均价-1,倍
s_tech_RVI,相对离散指数,Relative discrete index,"NUMBER(20,8)",(收盘-开盘)/(最高价-最低价),倍
s_tech_moneyflow20,20日资金流量,20-day fund flow,"NUMBER(20,8)",资金流量=MEAN(收盘价+最高价+最低价)*成交量,元
s_tech_EMV14,14日简易波动指标,Simple 14-day volatility index,"NUMBER(20,8)",(A-B)*C/成交额，其中，A=(最高+最低)/2；B=(昨高+昨低)/2；C=最高-最低；,
s_tech_EMV6,6日简易波动指标,6-day simple fluctuation index,"NUMBER(20,8)",(A-B)*C/成交额，其中，A=(最高+最低)/2；B=(昨高+昨低)/2；C=最高-最低；,
s_tech_aroon,阿隆指标,Aroon indicator,"NUMBER(20,8)",[(计算期天数-最高价后的天数)/计算期天数]*100，计算期天数为25,
s_tech_aroondown,阿隆向下指标,Aaron downward index,"NUMBER(20,8)",[(计算期天数-最低价后的天数)/计算期天数]*100，计算期天数为25,%
s_tech_aroonup,阿隆向上指标,Aron upward index,"NUMBER(20,8)",[(计算期天数-最高价后的天数)/计算期天数]*100，计算期天数为25,%
s_tech_AD6,6日收集派发指标,Collect and distribute indicators on the 6th day,"NUMBER(20,8)",IF 前收<今收，今收-MIN(前收，最低价) ELSE IF 前收>今收，今收-MAX(最高价，昨收) ELSE 0,元
s_tech_AD20,20日收集派发指标,Collection and distribution of indicators on the 20th,"NUMBER(20,8)",IF 前收<今收，今收-MIN(前收，最低价) ELSE IF 前收>今收，今收-MAX(最高价，昨收) ELSE 0,元
s_tech_AR,AR人气指标,Ar popularity index,"NUMBER(20,8)",26日内（当日最高价—当日开市价）之和 / 26日内（当日开市价—当日最低价）之和,倍
s_tech_BR,BR意愿指标,Br willingness index,"NUMBER(20,8)",26日（最高价－前收盘）和/26日（前收－最低价）和,倍
s_tech_ARBR,ARBR人气意愿指标,Arbr popularity willingness index,"NUMBER(20,8)",26日内（当日最高价—当日开市价）之和 / 26日内（当日开市价—当日最低价）之和,
s_tech_CR20,CR能量指标,Cr energy index,"NUMBER(20,8)",CR＝20日内(最高价－PM)之和/20日内(PM－最低价)之和；PM＝T-1日(最高价＋最低价＋收盘价)/3,倍
s_tech_bearpower,空头力道,Short-end force,"NUMBER(20,8)",空头力道，是计算Elder因子的中间变量。属于能量型因子。,
s_tech_bullpower,多头力道,Multi-pronged force,"NUMBER(20,8)",多头力道，是计算Elder因子的中间变量。属于能量型因子。,
s_tech_DHILO,波幅中位数,Median amplitude,"NUMBER(20,8)",每日对数最高价和对数最低价差值的3月内中位数,元
s_tech_skewness,股价偏度,Stock price skewness,"NUMBER(20,8)",20日收盘价的三阶矩,
