﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(38),,
SEC_ID,证券id,Securities ID,VARCHAR2(10),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
TRADE_DT,交易日期,Transaction Date,VARCHAR2(8),记录交易时的日期,
DEALNUM_TOTAL,总成交笔数,Total number of transactions,"NUMBER(20,0)",当日总的成交笔数,笔
OPEN_AUCTION_PRICE,开盘集合竞价成交价,Opening Auction Trading Price,"NUMBER(20,4)",记录最后一分钟集合竞价撮合的时间产生的成交价,元
OPEN_AUCTION_VOLUME,开盘集合竞价成交量,Opening Auction Trading Volume,"NUMBER(20,4)",记录开盘前五分钟，投资者卖出和买入价一致的最大量的股票竞价成交量,手
OPEN_AUCTION_DEALNUM,开盘集合竞价成交笔数,Opening Auction Number of Deals,"NUMBER(20,4)",记录开盘前五分钟集合竞价的成交笔数,笔
MAXUP,涨停价,High Limit Price,"NUMBER(20,4)",记录该只股票在证券交易总当涨幅达到规定时，便不可以继续上涨，这时的价格是涨停价,元
MAXDOWN,跌停价,Low Limit Price,"NUMBER(20,4)",记录该只股票在证券交易中当跌幅达到规定时，便不可以继续下跌，这时的价格是跌停价,元
CLOSE_AUCTION_PRICE,收盘集合竞价成交价,Closing Auction Trading Price,"NUMBER(20,4)",记录不高于申报买入价不低于申报卖出价的原则，集合讲收市所委托报价程序的成交价格,元
CLOSE_AUCTION_VOLUME,收盘集合竞价成交量,Closing Auction Trading Volume,"NUMBER(20,4)",记录收盘集合竞价成交量,手
CLOSE_AUCTION_DEALNUM,收盘集合竞价成交笔数,Closing Auction Number of Deals,"NUMBER(20,4)",记录收盘集合竞价成交笔数,笔
ASK_SIZE_VOLUME,[废弃]内盘成交量,[Discarded]Trading Volume of Ask Size,"NUMBER(20,4)",记录以买家的买入价成交的交易总量,手
BID_SIZE_VOLUME,[废弃]外盘成交量,[Discarded]Trading Volume of Bid Size,"NUMBER(20,4)",记录以卖家的卖出价成交的交易总量,手
