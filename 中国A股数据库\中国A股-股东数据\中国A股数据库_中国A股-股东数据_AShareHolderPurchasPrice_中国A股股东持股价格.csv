﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
SEC_ID,证券ID,Securities ID,VARCHAR2(10),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
CHANGE_DT,变动日期,Date of change,VARCHAR2(8),初始交易事件发生时，日期为市场交易对手获得股票的日期；后续分红事件发生时，日期为除权除息日,
S_INFO_PURCHASPRICE,持股价格,Purchasing Price,"NUMBER(20,4)",市场交易对手获得股票的成本价，除权价。,元
IS_VALID,是否有效,Is it effective,"NUMBER(1,0)",1=有效，后续继续除权；0=失效，不再除权,
S_INFO_INVALID_DT,失效日期,Expiration date,VARCHAR2(8),事件价格停止除权的日期,
S_INFO_EVENTCATEGORYCODE,事件类型代码,Event type code,"NUMBER(9,0)",影响期间股票价格的事件类型,
