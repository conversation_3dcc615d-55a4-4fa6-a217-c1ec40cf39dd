﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(100),万得定义的内部代码，用于标识公司唯一性；万得自定义的用来识别公司的唯一编码；证券产品在万得库中对应的唯一标识ID；,
S_INFO_DIMENSION,一级关系维度名称,Tier 1 relationship dimension name,VARCHAR2(100),,
S_INFO_DIMENSION1,二级关系维度名称,Secondary relationship dimension name,VARCHAR2(100),,
S_INFO_COMP_NAME,公司名称,company name,VARCHAR2(100),公司注册的合法名称；公司或企业组织的名称；基金产品的公布名称；证券或公司对应的中文全称；公司的称呼；,
S_INFO_COMP_SNAME,公司中文简称,Company Chinese Abbreviation,VARCHAR2(100),公司公布的中文简称；公司中文简称；公司或企业组织的中文名称缩写；公司的简化名称；公司或证券的中文简称；公司名称的中文简短称呼；,
S_ENDDATE,截止日期,Deadline,VARCHAR2(8),YYYYMM,
S_PRODUCT_NAME,产品名称,Product Name,VARCHAR2(100),业务的名称,
S_PRODUCT_NUMBER,数量,Quantity,"NUMBER(20,4)",最小单位对应的数量，如公布万元，折算为元,
NUMBER_TYPECODE,数量类型代码,Quantity type code,"NUMBER(9,0)",万得自定义的业务类型,
FREQUENCY_CODE,频率代码,Frequency code,"NUMBER(9,0)",关联0003表，月，季，半年，年，133开头,
