﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,OBJECT_ID,VARCHAR2(50),,
WIND_ID,万得机构统一编码,,VARCHAR2(10),,
COMPANY_NAME,公司名称,,VARCHAR2(500),,
POSITION_NAME,职位名称,,VARCHAR2(1000),,
ADDRESS,上班地址,,VARCHAR2(2000),,
FIRST_RELEASE_TIME,招聘首次发布时间,,VARCHAR2(8),,
FUNCTION_TYPE,职能类别,,VARCHAR2(200),,
JOB_REQUIREMENT,任职要求,,VARCHAR2(500),,
KEW_WORD,职位关键字,,VARCHAR2(2000),,
LANGUAGE_REQUIREMENT,语言要求,,VARCHAR2(200),,
MAX_AGE_REQUIREMENT,最高年龄要求,,"NUMBER(2,0)",,
MAX_SALARY,最高薪水,,"NUMBER(20,0)",招聘公司能发放给应聘者的最低薪水,元
MAX_SALARY_PAYMENTM,薪资最大发放月数,,"NUMBER(20,0)",招聘公司对于员工薪资发放月数的最大数量,月
MAX_WORK_EXPERIENCE,最高工作年限,,"NUMBER(20,0)",招聘公司对于应聘者工作经验的最高要求,年
MIN_AGE_REQUIREMENT,最低年龄要求,,"NUMBER(2,0)",,
MIN_SALARY,最低薪水,,"NUMBER(20,0)",招聘公司能发放给应聘者的最低薪水,元
MIN_SALARY_PAYMENTM,薪资最小发放月数,,"NUMBER(20,0)",招聘公司对于员工薪资发放月数的最小数量,月
MIN_WORK_EXPERIENCE,最低工作年限,,"NUMBER(20,0)",招聘公司对于应聘者工作经验的最低要求；0代表的含义是应届/在校、应届生、在校生,年
NUMBER_OF_RECRUITMENT,招聘人数,,VARCHAR2(100),,
POSITION_TYPE,职位类型,,VARCHAR2(100),,
RECRUITMENT_CHANNEL,招聘渠道名称,,VARCHAR2(100),,
RELEASE_TIME,招聘发布时间,,VARCHAR2(8),,
RESPONSIBILITY,岗位职责,,VARCHAR2(4000),,
SALARY_NEGOTIABLE,薪水是否面议,,"NUMBER(1,0)",0或1；0代表否；1代表是,
WELFARE,福利,,VARCHAR2(1000),,
WORK_PLACE,工作地点,,VARCHAR2(2000),,
