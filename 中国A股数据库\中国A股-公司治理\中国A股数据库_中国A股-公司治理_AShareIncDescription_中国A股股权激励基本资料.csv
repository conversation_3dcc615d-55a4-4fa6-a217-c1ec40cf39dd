﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_INC_SEQUENCE,序号,Serial number,VARCHAR2(20),区别不同次股权激励方案的编号,
S_INC_SUBJECT,激励标的,Incentive target,"NUMBER(9,0)",1.期权2.股票3.股票增值权。请参考S_REFRMSCHM_INCENTIVESUBCODE。,
S_INC_TYPE,激励方式(废弃),Incentive method(Abandoned),"NUMBER(9,0)",因为该字段无法准确及时更新，所以废弃。请参考S_REFRMSCHM_INCENTIVEMETCODE,
S_INC_QUANTITY,激励总数(万股/万份),"Total incentives (10,000 shares / 10,000 shares)","NUMBER(20,4)",,
S_INC_FIRSTINC,起始日,Start date,VARCHAR2(8),,
S_INC_ENDINC,到期日,expiry date,VARCHAR2(8),激励对象获授的限制性股票/股权期权/股票增值权全部解除限售或回购注销的最后日期,
S_INC_INITEXECPRI,期权初始行权价格(股票转让价格),Option exercise price (stock transfer price),"NUMBER(20,4)",,
S_INC_EXPIRYDATE,有效期,Validity period,"NUMBER(20,4)",,年
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期,
S_INC_PROGRAMDESCRIPT,方案说明,plan description,VARCHAR2(3000),关于激励方式/激励数量及其来源/激励价格/激励有效期的说明,
S_INC_INCENTSHARESALEDESCRIPT,激励股票出售说明,Incentive stock sale instructions,VARCHAR2(4000),激励对象获得的限制性股票/股权期权的转让条件,
S_INC_INCENTCONDITION,激励授予条件,Incentive grant condition,VARCHAR2(4000),股权激励可以进行授予的前提条件,
S_INC_OPTEXESPECIALCONDITION,期权行权特别条件,Special conditions for option exercise,VARCHAR2(4000),上市公司对于本次授予的股权激励数量解除限售设置的考核条件,
PROGRESS,方案进度,Program progress,VARCHAR2(10),万得自定义的用于反映事件最新进展的唯一编码,
PRICE_DESCRIPTION,价格说明,Price Description,VARCHAR2(200),上市公司向激励对象授予限制性股票/股票期权/股票增值权时确定的激励对象购买上市公司股份的价格变动说明,
INC_NUMBERS_RATE,激励数量占当前总股本比例(%),The number of incentives accounts for the current total equity ratio (%),"NUMBER(20,4)",上市公司在股权激励授予的股票/期权总量占总股本的百分比,
PREPLAN_ANN_DATE,预案公告日,Plan announcement day,VARCHAR2(8),股权激励草案公告发布当天的日期,
GM_DATE,股东大会公告日,Announcement date of shareholders' meeting,VARCHAR2(8),上市公司股东大会审议完成公告发布当天的日期,
IMPLEMENT_DATE,首次实施公告日,First implementation announcement day,VARCHAR2(8),开始实施股权激励的公告发布当天的日期,
INC_FUND_DESCRIPTION,激励基金说明,Incentive fund description,VARCHAR2(2000),激励基金的计提及计算标准,
INTERVAL_MONTHS,授权日与首次可行权日间隔时间(月),Authorization date and the first vesting date interval (months),"NUMBER(20,4)",,
EQINC_PLAN_EVENT_ID,股权激励事件ID,Equity incentive event ID,VARCHAR2(40),万得自定义的用来识别事件的ID,
S_REFRMSCHM_INCENTIVESUBCODE,激励标的类型代码,Type Code of Subject Matter of Incentive Stock Option,"NUMBER(9,0)",万得自定义的用来区别激励所使用对象的唯一编码,
S_REFRMSCHM_INCENTIVEMETCODE,激励方式类型代码,Type Code of Method of Incentive,"NUMBER(9,0)",万得自定义的用于区别股权激励的股票来源的唯一编码,
LOCKUP_PERIOD_Y,锁定期,Lock-up Period,"NUMBER(20,4)",,
UNLOCK_PERIOD_Y,解锁期,Unlocking Period,"NUMBER(20,4)",,
