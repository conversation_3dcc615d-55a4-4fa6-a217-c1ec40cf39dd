﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(40),Wind自编代码，公司唯一性,
REPORT_PERIOD,报告期,Reporting period,VARCHAR2(8),报告内容记录的截止时间点，报告成果的时期；财务报告披露的财务数据对应的时间；,
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期；有多个阶段的事件，首次披露该事件的日期；公告发布当天的日期，多阶段事件首次披露的日期；,
S_INFO_COMPNAME,单位名称,Company Name,VARCHAR2(500),"按公布名称加工,若公布名称超过总长度会截取部分保留",
S_INFO_COMPCODE2,单位名称ID,Unit name ID,VARCHAR2(40),Wind自编代码，公司唯一性，部分未收录公司该字段为空,
TYPECODE,往来资金类别代码,Current fund category code,"NUMBER(9,0)","*********:长期借款;
*********:一年内到期的长期借款",
CONDITIONCODE,往来资金条件代码,Current fund condition code,"NUMBER(9,0)",附注1,
START_DT,起始日,Start date,VARCHAR2(8),长期借款的开始时间点；产生往来资金的起始日期；,
END_DT,终止日,Termination date,VARCHAR2(8),长期借款的截止时间点；产生往来资金的终止日期；,
AMOUNT1,金额（元）,Amount (yuan),"NUMBER(20,4)",货币硬币的面值；往来资金的金额；,元
AMOUNT2,原币金额（元）,Original currency amount (yuan),"NUMBER(20,4)",本国以外的外币；以原币种(财报所采用的币种)计量的往来资金的金额；,
CRNCY_CODE,货币代码,Currency code,VARCHAR2(10),"附注2
货币代码一般针对原币金额，原币金额为0或为空时，针对本币金额。若本币金额和原币金额都不为空，则货币代码针对原币金额，本币金额单位为人民币元。",
RATE,利率(%),interest rate(%),VARCHAR2(200),一定时间内利息额与借贷资金额的比，往来资金所产生的利率,
