#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能对比测试：原版本 vs 统一数据处理版本
"""

import time
import pandas as pd
from pathlib import Path
import numpy as np

def create_mock_data():
    """创建模拟数据用于性能测试"""
    print("创建模拟数据...")
    
    # 创建模拟的交易日期
    dates = pd.date_range('2024-01-01', '2024-03-31', freq='D')
    trading_dates = dates[dates.weekday < 5]  # 只保留工作日
    
    # 创建模拟的可转债代码
    bond_codes = [f'bond_{i:03d}' for i in range(1, 101)]  # 100只可转债
    stock_codes = [f'stock_{i:03d}' for i in range(1, 101)]  # 100只正股
    
    # 创建模拟数据
    mock_data = {
        'trading_dates': pd.DataFrame({'TRADE_DT': trading_dates.strftime('%Y%m%d')}),
        'bond_valuation': [],
        'bond_balance': [],
        'bond_rating': [],
        'bond_stock_mapping': pd.DataFrame({
            'S_INFO_WINDCODE': bond_codes,
            'S_INFO_UNDERLYINGWINDCODE': stock_codes
        }),
        'stock_prices': [],
        'stock_market_cap': [],
        'stock_financial': [],
        'bond_prices': []
    }
    
    # 为每个交易日创建估值数据
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for bond_code in bond_codes:
            mock_data['bond_valuation'].append({
                'S_INFO_WINDCODE': bond_code,
                'TRADE_DT': date_str,
                'CB_ANAL_YTM': np.random.uniform(0.01, 0.08),
                'CB_ANAL_PTM': np.random.uniform(0.5, 3.0),
                'CB_ANAL_CONVPREMIUMRATIO': np.random.uniform(0.05, 0.30)
            })
            
            mock_data['bond_prices'].append({
                'S_INFO_WINDCODE': bond_code,
                'TRADE_DT': date_str,
                'S_DQ_CLOSE': np.random.uniform(90, 150)
            })
        
        for stock_code in stock_codes:
            mock_data['stock_prices'].append({
                'S_INFO_WINDCODE': stock_code,
                'TRADE_DT': date_str,
                'S_DQ_CLOSE': np.random.uniform(3, 50),
                'S_DQ_AMOUNT': np.random.uniform(5000000, 50000000)
            })
            
            mock_data['stock_market_cap'].append({
                'S_INFO_WINDCODE': stock_code,
                'TRADE_DT': date_str,
                'S_VAL_MV': np.random.uniform(20, 200)
            })
    
    # 转换为DataFrame
    mock_data['bond_valuation'] = pd.DataFrame(mock_data['bond_valuation'])
    mock_data['bond_prices'] = pd.DataFrame(mock_data['bond_prices'])
    mock_data['stock_prices'] = pd.DataFrame(mock_data['stock_prices'])
    mock_data['stock_market_cap'] = pd.DataFrame(mock_data['stock_market_cap'])
    
    # 创建余额数据（每只债券一条记录）
    mock_data['bond_balance'] = pd.DataFrame({
        'S_INFO_WINDCODE': bond_codes,
        'S_INFO_CHANGEDATE': '20240101',
        'B_INFO_OUTSTANDINGBALANCE': np.random.uniform(5, 20)
    })
    
    # 创建评级数据（每只债券一条记录）
    ratings = ['AAA', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-']
    mock_data['bond_rating'] = pd.DataFrame({
        'S_INFO_WINDCODE': bond_codes,
        'ANN_DT': '20240101',
        'B_INFO_CREDITRATING': np.random.choice(ratings, len(bond_codes))
    })
    
    # 创建财务数据（每只股票一条记录）
    mock_data['stock_financial'] = pd.DataFrame({
        'S_INFO_WINDCODE': stock_codes,
        'ANN_DT': '20240101',
        'NET_PROFIT_EXCL_MIN_INT_INC': np.random.uniform(-1000000, 5000000)
    })
    
    print(f"模拟数据创建完成:")
    print(f"  - 交易日期: {len(mock_data['trading_dates'])} 天")
    print(f"  - 可转债数量: {len(bond_codes)} 只")
    print(f"  - 估值数据: {len(mock_data['bond_valuation'])} 条")
    print(f"  - 价格数据: {len(mock_data['stock_prices'])} 条")
    
    return mock_data

def simulate_original_approach(mock_data):
    """模拟原版本的数据处理方式"""
    print("\n" + "=" * 60)
    print("模拟原版本数据处理方式")
    print("=" * 60)
    
    start_time = time.time()
    
    # 获取月末日期（简化为每月最后一个交易日）
    trading_dates = pd.to_datetime(mock_data['trading_dates']['TRADE_DT'])
    month_ends = []
    for month in [1, 2, 3]:
        month_dates = trading_dates[trading_dates.dt.month == month]
        if not month_dates.empty:
            month_ends.append(month_dates.max().strftime('%Y%m%d'))
    
    print(f"需要处理 {len(month_ends)} 个月末日期")
    
    # 模拟原版本：每个日期都重新加载和合并数据
    total_operations = 0
    for date in month_ends:
        print(f"处理日期: {date}")
        
        # 1. 加载数据（模拟数据库查询）
        load_start = time.time()
        bond_valuation = mock_data['bond_valuation'][mock_data['bond_valuation']['TRADE_DT'] == date].copy()
        bond_balance = mock_data['bond_balance'].copy()
        bond_rating = mock_data['bond_rating'].copy()
        bond_stock_mapping = mock_data['bond_stock_mapping'].copy()
        stock_prices = mock_data['stock_prices'][mock_data['stock_prices']['TRADE_DT'] == date].copy()
        stock_market_cap = mock_data['stock_market_cap'][mock_data['stock_market_cap']['TRADE_DT'] == date].copy()
        stock_financial = mock_data['stock_financial'].copy()
        load_time = time.time() - load_start
        
        # 2. 数据合并（模拟多次merge操作）
        merge_start = time.time()
        merged_data = bond_valuation.merge(bond_balance, on='S_INFO_WINDCODE', how='left')
        merged_data = merged_data.merge(bond_rating, on='S_INFO_WINDCODE', how='left')
        merged_data = merged_data.merge(bond_stock_mapping, on='S_INFO_WINDCODE', how='left')
        
        # 合并正股数据
        stock_data = stock_prices.merge(stock_market_cap, on='S_INFO_WINDCODE', how='left')
        stock_data = stock_data.merge(stock_financial, on='S_INFO_WINDCODE', how='left')
        
        final_data = merged_data.merge(
            stock_data,
            left_on='S_INFO_UNDERLYINGWINDCODE',
            right_on='S_INFO_WINDCODE',
            how='left',
            suffixes=('_bond', '_stock')
        )
        merge_time = time.time() - merge_start
        
        # 3. 筛选（模拟筛选操作）
        filter_start = time.time()
        # 简化的筛选逻辑
        filtered_data = final_data[
            (final_data['S_DQ_CLOSE'] >= 4.0) &
            (final_data['B_INFO_OUTSTANDINGBALANCE'] >= 5.0) &
            (final_data['S_VAL_MV'] >= 40.0)
        ]
        filter_time = time.time() - filter_start
        
        total_operations += len(final_data)
        print(f"  - 数据加载: {load_time:.3f}s")
        print(f"  - 数据合并: {merge_time:.3f}s")
        print(f"  - 数据筛选: {filter_time:.3f}s")
        print(f"  - 处理记录: {len(final_data)} 条")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n原版本总结:")
    print(f"  - 总耗时: {total_time:.3f}s")
    print(f"  - 总操作数: {total_operations}")
    print(f"  - 平均每次操作: {total_time/len(month_ends):.3f}s")
    
    return total_time, total_operations

def simulate_unified_approach(mock_data):
    """模拟统一数据处理版本的方式"""
    print("\n" + "=" * 60)
    print("模拟统一数据处理版本方式")
    print("=" * 60)
    
    start_time = time.time()
    
    # 1. 一次性创建统一数据集
    print("创建统一数据集...")
    preprocess_start = time.time()
    
    # 预处理时间字段
    bond_valuation = mock_data['bond_valuation'].copy()
    bond_valuation['TRADE_DT'] = pd.to_datetime(bond_valuation['TRADE_DT'])
    stock_prices = mock_data['stock_prices'].copy()
    stock_prices['TRADE_DT'] = pd.to_datetime(stock_prices['TRADE_DT'])
    stock_market_cap = mock_data['stock_market_cap'].copy()
    stock_market_cap['TRADE_DT'] = pd.to_datetime(stock_market_cap['TRADE_DT'])
    
    # 创建基础数据框架
    base_data = bond_valuation.copy()
    
    # 合并映射关系
    base_data = base_data.merge(mock_data['bond_stock_mapping'], on='S_INFO_WINDCODE', how='left')
    
    # 合并价格数据
    bond_prices_processed = mock_data['bond_prices'].copy()
    bond_prices_processed['TRADE_DT'] = pd.to_datetime(bond_prices_processed['TRADE_DT'])
    base_data = base_data.merge(
        bond_prices_processed.rename(columns={'S_DQ_CLOSE': 'BOND_PRICE'}),
        on=['S_INFO_WINDCODE', 'TRADE_DT'], how='left'
    )
    
    # 合并正股数据
    base_data = base_data.merge(
        stock_prices.rename(columns={'S_DQ_CLOSE': 'STOCK_PRICE', 'S_DQ_AMOUNT': 'STOCK_AMOUNT'}),
        left_on=['S_INFO_UNDERLYINGWINDCODE', 'TRADE_DT'],
        right_on=['S_INFO_WINDCODE', 'TRADE_DT'], how='left',
        suffixes=('', '_stock')
    )
    
    base_data = base_data.merge(
        stock_market_cap.rename(columns={'S_VAL_MV': 'STOCK_MARKET_CAP'}),
        left_on=['S_INFO_UNDERLYINGWINDCODE', 'TRADE_DT'],
        right_on=['S_INFO_WINDCODE', 'TRADE_DT'], how='left',
        suffixes=('', '_mv')
    )
    
    # 添加余额、评级、财务数据（简化处理）
    base_data = base_data.merge(
        mock_data['bond_balance'][['S_INFO_WINDCODE', 'B_INFO_OUTSTANDINGBALANCE']],
        on='S_INFO_WINDCODE', how='left'
    )
    base_data = base_data.merge(
        mock_data['bond_rating'][['S_INFO_WINDCODE', 'B_INFO_CREDITRATING']],
        on='S_INFO_WINDCODE', how='left'
    )
    base_data = base_data.merge(
        mock_data['stock_financial'][['S_INFO_WINDCODE', 'NET_PROFIT_EXCL_MIN_INT_INC']],
        left_on='S_INFO_UNDERLYINGWINDCODE',
        right_on='S_INFO_WINDCODE', how='left',
        suffixes=('', '_fin')
    )
    
    # 重命名列
    unified_dataset = base_data.rename(columns={
        'S_INFO_WINDCODE': 'BOND_CODE',
        'TRADE_DT': 'TRADE_DATE',
        'CB_ANAL_YTM': 'YTM',
        'STOCK_PRICE': 'STOCK_PRICE',
        'STOCK_MARKET_CAP': 'STOCK_MARKET_CAP',
        'B_INFO_OUTSTANDINGBALANCE': 'BOND_BALANCE',
        'STOCK_AMOUNT': 'STOCK_AMOUNT'
    })
    
    preprocess_time = time.time() - preprocess_start
    print(f"统一数据集创建完成: {preprocess_time:.3f}s")
    print(f"统一数据集大小: {len(unified_dataset)} 条记录")
    
    # 2. 批量筛选
    print("执行批量筛选...")
    filter_start = time.time()
    
    # 获取月末日期
    trading_dates = pd.to_datetime(mock_data['trading_dates']['TRADE_DT'])
    month_ends = []
    for month in [1, 2, 3]:
        month_dates = trading_dates[trading_dates.dt.month == month]
        if not month_dates.empty:
            month_ends.append(month_dates.max().strftime('%Y%m%d'))
    
    # 批量筛选所有日期
    all_results = {}
    for date in month_ends:
        date_data = unified_dataset[unified_dataset['TRADE_DATE'].dt.strftime('%Y%m%d') == date]
        
        # 应用筛选条件
        filtered_data = date_data[
            (date_data['STOCK_PRICE'] >= 4.0) &
            (date_data['BOND_BALANCE'] >= 5.0) &
            (date_data['STOCK_MARKET_CAP'] >= 40.0)
        ]
        
        all_results[date] = len(filtered_data)
    
    filter_time = time.time() - filter_start
    print(f"批量筛选完成: {filter_time:.3f}s")
    
    end_time = time.time()
    total_time = end_time - start_time
    total_operations = len(unified_dataset)
    
    print(f"\n统一版本总结:")
    print(f"  - 总耗时: {total_time:.3f}s")
    print(f"  - 预处理耗时: {preprocess_time:.3f}s")
    print(f"  - 筛选耗时: {filter_time:.3f}s")
    print(f"  - 总操作数: {total_operations}")
    print(f"  - 筛选结果: {all_results}")
    
    return total_time, total_operations, preprocess_time, filter_time

def main():
    """主函数"""
    print("=" * 80)
    print("可转债策略性能对比测试")
    print("=" * 80)
    
    # 创建模拟数据
    mock_data = create_mock_data()
    
    # 测试原版本方式
    original_time, original_ops = simulate_original_approach(mock_data)
    
    # 测试统一版本方式
    unified_time, unified_ops, preprocess_time, filter_time = simulate_unified_approach(mock_data)
    
    # 性能对比
    print("\n" + "=" * 80)
    print("性能对比结果")
    print("=" * 80)
    
    print(f"原版本:")
    print(f"  - 总耗时: {original_time:.3f}s")
    print(f"  - 处理记录数: {original_ops}")
    print(f"  - 每条记录耗时: {original_time/original_ops*1000:.3f}ms")
    
    print(f"\n统一版本:")
    print(f"  - 总耗时: {unified_time:.3f}s")
    print(f"  - 预处理耗时: {preprocess_time:.3f}s")
    print(f"  - 筛选耗时: {filter_time:.3f}s")
    print(f"  - 处理记录数: {unified_ops}")
    print(f"  - 每条记录耗时: {unified_time/unified_ops*1000:.3f}ms")
    
    print(f"\n性能提升:")
    speedup = original_time / unified_time
    print(f"  - 总体加速比: {speedup:.2f}x")
    print(f"  - 耗时减少: {(1-unified_time/original_time)*100:.1f}%")
    
    if speedup > 1:
        print(f"  - ✅ 统一版本性能更优")
    else:
        print(f"  - ⚠️ 原版本性能更优")
    
    print(f"\n优势分析:")
    print(f"  - 统一版本减少了重复的数据加载和合并操作")
    print(f"  - 批量筛选比逐个日期筛选更高效")
    print(f"  - 预处理一次，多次使用，摊薄了预处理成本")
    print(f"  - 注意：统一版本处理了全量数据({unified_ops}条)，原版本只处理筛选日期数据({original_ops}条)")
    print(f"  - 在大数据集和多次查询场景下，统一版本优势更明显")

    # 计算每条记录的处理效率
    original_efficiency = original_time / original_ops * 1000
    unified_efficiency = unified_time / unified_ops * 1000
    efficiency_improvement = (original_efficiency - unified_efficiency) / original_efficiency * 100

    print(f"\n处理效率对比:")
    print(f"  - 原版本每条记录: {original_efficiency:.3f}ms")
    print(f"  - 统一版本每条记录: {unified_efficiency:.3f}ms")
    print(f"  - 效率提升: {efficiency_improvement:.1f}%")

    if efficiency_improvement > 0:
        print(f"  - ✅ 统一版本单条记录处理效率更高")
    else:
        print(f"  - ⚠️ 原版本单条记录处理效率更高")

if __name__ == "__main__":
    main()
