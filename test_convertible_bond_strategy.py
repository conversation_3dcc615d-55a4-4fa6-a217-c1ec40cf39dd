#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试可转债量化策略的基本功能
"""

import pandas as pd
import numpy as np
from datetime import datetime
from get_db_funs import get_db_data
from convertible_bond_strategy import ConvertibleBondStrategy

def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    
    try:
        # 测试获取交易日历
        calendar_data = get_db_data('AShareCalendar',
                                  keywords=['TRADE_DAYS'],
                                  additional_conditions={
                                      'S_INFO_EXCHMARKET': 'SSE',
                                      'TRADE_DAYS': ['20240101', '20240131']
                                  })
        
        if calendar_data is not None and not calendar_data.empty:
            print(f"✓ 交易日历数据获取成功，共 {len(calendar_data)} 条记录")
            print(f"  示例数据: {calendar_data.head(3)['TRADE_DAYS'].tolist()}")
        else:
            print("✗ 交易日历数据获取失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False
    
    return True

def test_convertible_bond_list():
    """测试获取可转债列表"""
    print("\n测试获取可转债列表...")
    
    try:
        # 测试获取可转债基本信息
        bond_data = get_db_data('CBondDescription', 
                              keywords=['S_INFO_WINDCODE', 'S_INFO_NAME', 'B_INFO_SPECIALBONDTYPE'], 
                              additional_conditions={
                                  'OR': [
                                      {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%可转债%')},
                                      {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%可转换%')},
                                      {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%转债%')}
                                  ]
                              })
        
        if bond_data is not None and not bond_data.empty:
            # 筛选SZ和SH后缀的可转债
            pattern = r'\.(SZ|SH)$'
            mask = bond_data['S_INFO_WINDCODE'].str.contains(pattern, regex=True, na=False)
            bond_data = bond_data[mask]
            
            print(f"✓ 可转债数据获取成功，共 {len(bond_data)} 条记录")
            print(f"  示例代码: {bond_data.head(3)['S_INFO_WINDCODE'].tolist()}")
            return bond_data['S_INFO_WINDCODE'].tolist()[:10]  # 返回前10个用于测试
        else:
            print("✗ 可转债数据获取失败")
            return []
            
    except Exception as e:
        print(f"✗ 可转债列表获取失败: {e}")
        return []

def test_convertible_bond_data(bond_codes, test_date='20240131'):
    """测试获取可转债衍生指标数据"""
    print(f"\n测试获取可转债衍生指标数据 (日期: {test_date})...")
    
    if not bond_codes:
        print("✗ 没有可转债代码用于测试")
        return
    
    try:
        # 测试获取可转债衍生指标
        cb_data = get_db_data('CCBondValuation', 
                            keywords=['S_INFO_WINDCODE', 'TRADE_DT', 'CB_ANAL_YTM', 'CB_ANAL_PTM'], 
                            additional_conditions={
                                'S_INFO_WINDCODE': bond_codes,
                                'TRADE_DT': test_date
                            })
        
        if cb_data is not None and not cb_data.empty:
            print(f"✓ 可转债衍生指标数据获取成功，共 {len(cb_data)} 条记录")
            print(f"  示例数据:")
            print(cb_data.head(3)[['S_INFO_WINDCODE', 'CB_ANAL_YTM', 'CB_ANAL_PTM']])
        else:
            print("✗ 可转债衍生指标数据获取失败")
            
    except Exception as e:
        print(f"✗ 可转债衍生指标数据获取失败: {e}")

def test_stock_data():
    """测试获取股票数据"""
    print(f"\n测试获取股票数据...")
    
    try:
        # 测试获取几个股票的价格数据
        test_stocks = ['000001.SZ', '000002.SZ', '600000.SH']
        test_date = '20240131'
        
        stock_data = get_db_data('AShareEODPrices', 
                               keywords=['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE'], 
                               additional_conditions={
                                   'S_INFO_WINDCODE': test_stocks,
                                   'TRADE_DT': test_date
                               })
        
        if stock_data is not None and not stock_data.empty:
            print(f"✓ 股票价格数据获取成功，共 {len(stock_data)} 条记录")
            print(f"  示例数据:")
            print(stock_data[['S_INFO_WINDCODE', 'S_DQ_CLOSE']])
        else:
            print("✗ 股票价格数据获取失败")
            
    except Exception as e:
        print(f"✗ 股票数据获取失败: {e}")

def test_strategy_initialization():
    """测试策略初始化"""
    print(f"\n测试策略初始化...")
    
    try:
        strategy = ConvertibleBondStrategy(start_date='2024-01-01', end_date='2024-01-31')
        print("✓ 策略初始化成功")
        
        # 测试获取交易日历
        trade_dates = strategy.get_trading_dates()
        if trade_dates:
            print(f"✓ 交易日历获取成功，共 {len(trade_dates)} 个交易日")
            
            # 测试获取月末日期
            month_ends = strategy.get_month_end_dates(trade_dates)
            if month_ends:
                print(f"✓ 月末日期获取成功，共 {len(month_ends)} 个月末日期: {month_ends}")
            else:
                print("✗ 月末日期获取失败")
        else:
            print("✗ 交易日历获取失败")
            
    except Exception as e:
        print(f"✗ 策略初始化失败: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("可转债量化策略测试")
    print("=" * 60)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("数据库连接失败，终止测试")
        return
    
    # 2. 测试获取可转债列表
    bond_codes = test_convertible_bond_list()
    
    # 3. 测试获取可转债数据
    if bond_codes:
        test_convertible_bond_data(bond_codes)
    
    # 4. 测试获取股票数据
    test_stock_data()
    
    # 5. 测试策略初始化
    test_strategy_initialization()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
