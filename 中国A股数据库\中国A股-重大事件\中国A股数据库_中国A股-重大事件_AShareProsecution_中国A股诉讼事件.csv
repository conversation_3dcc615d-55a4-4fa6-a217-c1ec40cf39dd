﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(40),Wind自编代码，公司唯一性,
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期,
TITLE,案件名称,Case name,VARCHAR2(40),诉讼案件名称；诉讼案件的概况性称呼；,
ACCUSER,原告方,Plaintiff,VARCHAR2(3000),民事诉讼中向法院起诉的人；提起诉讼的一方当事人；,
DEFENDANT,被告方,Defendant,VARCHAR2(3000),民事诉讼中被法院起诉的人；被提起诉讼的一方当事人；,
PRO_TYPE,诉讼类型,Type of litigation,VARCHAR2(10),根据案件性质和所依据的法律分类；诉讼所属的类型；,
AMOUNT,涉案金额,Amount involved,"NUMBER(20,4)",与诉讼事件有直接关系的金额,元
CRNCY_CODE,货币代码,Currency code,VARCHAR2(10),在外汇市场或者货币市场上用于交易的货币种类；记录一国货币在交易中为了方便而使用的代码；,
PROSECUTE_DT,起诉日期,Date of prosecution,VARCHAR2(8),提起诉讼的时间；原告方提起诉讼的日期；,
COURT,一审受理法院,Court of first instance,VARCHAR2(200),公司注册的合法名称；公司或企业组织的名称；基金产品的公布名称；证券或公司对应的中文全称；公司的称呼；,
JUDGE_DT,判决日期,Date of judgment,VARCHAR2(8),法院出具的一审判决书的日期或仲裁机构出具的一审民事裁决书的日期,
RESULT,判决内容,Judgment content,VARCHAR2(4000),法院出具的一审判决书的内容或仲裁机构出具的一审民事裁决书的内容,
IS_APPEAL,是否上诉,Whether to appeal,"NUMBER(5,0)",原告或被告是否对一审判决结果不服，提出上诉。0:不上诉；1：上诉,
APPELLANT,二审上诉方(是否原告),Second-instance appellant (whether plaintiff),VARCHAR2(1),0:被告上诉；1：原告上诉,
COURT2,二审受理法院,Second instance court,VARCHAR2(200),公司注册的合法名称；公司或企业组织的名称；基金产品的公布名称；证券或公司对应的中文全称；公司的称呼；,
JUDGE_DT2,二审判决日期,Second trial date,VARCHAR2(8),法院出具的二审判决书的日期或仲裁机构出具的二审民事裁决书的日期,
RESULT2,二审判决内容,Second instance judgment content,VARCHAR2(2000),法院出具的二审判决书的内容或仲裁机构出具的二审民事裁决书的内容,
RESULTAMOUNT,判决金额,Judgment amount,"NUMBER(20,4)",法院出具的一审判决书的涉案判决金额或仲裁机构出具的一审民事裁决书的涉案判决金额,元
BRIEFRESULT,诉讼结果,Litigation result,VARCHAR2(100),原告方所处的诉讼最新进度,
EXECUTION,执行情况,Implementation,VARCHAR2(4000),案件目前的最新进展情况,
INTRODUCTION,案件描述,Case description,CLOB,诉讼案件描述；诉讼案件的基本情况表述；,
LITIGATION_EVENTS_ID,诉讼事件ID,Litigation event ID,VARCHAR2(40),万得自定义的用来识别诉讼事件的唯一编码,
BRIEFRESULT_CODE,诉讼结果类型代码,Litigation result type code,"NUMBER(9,0)",万得自定义的用来识别诉讼结果的编码,
