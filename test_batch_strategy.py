#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试批量数据版本的可转债策略
"""

import pandas as pd
from pathlib import Path
from convertible_bond_strategy_batch import ConvertibleBondStrategyBatch

def test_data_fetching():
    """测试数据获取功能"""
    print("=" * 60)
    print("测试数据获取功能")
    print("=" * 60)
    
    # 创建策略实例（短期测试）
    strategy = ConvertibleBondStrategyBatch('2024-01-01', '2024-03-31')
    
    # 测试交易日历获取
    print("\n1. 测试交易日历获取...")
    trading_dates = strategy.fetch_all_trading_dates()
    if not trading_dates.empty:
        print(f"✅ 交易日历获取成功: {len(trading_dates)} 个交易日")
        print(f"   开始日期: {trading_dates['TRADE_DT'].min()}")
        print(f"   结束日期: {trading_dates['TRADE_DT'].max()}")
    else:
        print("❌ 交易日历获取失败")
        return False
    
    # 测试可转债基础信息获取
    print("\n2. 测试可转债基础信息获取...")
    bond_info = strategy.fetch_all_convertible_bonds()
    if not bond_info.empty:
        print(f"✅ 可转债基础信息获取成功: {len(bond_info)} 只")
        print(f"   示例债券: {bond_info['S_INFO_WINDCODE'].head(3).tolist()}")
    else:
        print("❌ 可转债基础信息获取失败")
        return False
    
    # 获取少量债券代码进行测试
    test_bonds = bond_info['S_INFO_WINDCODE'].head(10).tolist()
    print(f"\n使用 {len(test_bonds)} 只债券进行后续测试")
    
    # 测试可转债估值数据获取
    print("\n3. 测试可转债估值数据获取...")
    valuation_data = strategy.fetch_all_bond_valuation_data(test_bonds)
    if not valuation_data.empty:
        print(f"✅ 可转债估值数据获取成功: {len(valuation_data)} 条")
    else:
        print("⚠️ 可转债估值数据获取失败或为空")
    
    # 测试可转债余额数据获取
    print("\n4. 测试可转债余额数据获取...")
    balance_data = strategy.fetch_all_bond_balance_data(test_bonds)
    if not balance_data.empty:
        print(f"✅ 可转债余额数据获取成功: {len(balance_data)} 条")
    else:
        print("⚠️ 可转债余额数据获取失败或为空")
    
    # 测试可转债评级数据获取
    print("\n5. 测试可转债评级数据获取...")
    rating_data = strategy.fetch_all_bond_rating_data(test_bonds)
    if not rating_data.empty:
        print(f"✅ 可转债评级数据获取成功: {len(rating_data)} 条")
    else:
        print("⚠️ 可转债评级数据获取失败或为空")
    
    # 测试可转债-正股映射关系获取
    print("\n6. 测试可转债-正股映射关系获取...")
    mapping_data = strategy.fetch_all_bond_stock_mapping(test_bonds)
    if not mapping_data.empty:
        print(f"✅ 映射关系获取成功: {len(mapping_data)} 条")
        
        # 获取正股代码进行测试
        stock_codes = mapping_data['S_INFO_UNDERLYINGWINDCODE'].dropna().head(5).tolist()
        if stock_codes:
            print(f"   测试正股代码: {stock_codes}")
            
            # 测试正股价格数据获取
            print("\n7. 测试正股价格数据获取...")
            stock_price_data = strategy.fetch_all_stock_price_data(stock_codes)
            if not stock_price_data.empty:
                print(f"✅ 正股价格数据获取成功: {len(stock_price_data)} 条")
            else:
                print("⚠️ 正股价格数据获取失败或为空")
            
            # 测试正股市值数据获取
            print("\n8. 测试正股市值数据获取...")
            market_cap_data = strategy.fetch_all_stock_market_cap_data(stock_codes)
            if not market_cap_data.empty:
                print(f"✅ 正股市值数据获取成功: {len(market_cap_data)} 条")
            else:
                print("⚠️ 正股市值数据获取失败或为空")
    else:
        print("❌ 映射关系获取失败")
    
    # 测试可转债价格数据获取
    print("\n9. 测试可转债价格数据获取...")
    bond_price_data = strategy.fetch_all_bond_price_data(test_bonds)
    if not bond_price_data.empty:
        print(f"✅ 可转债价格数据获取成功: {len(bond_price_data)} 条")
    else:
        print("⚠️ 可转债价格数据获取失败或为空")
    
    print("\n" + "=" * 60)
    print("数据获取测试完成")
    print("=" * 60)
    return True

def test_strategy_screening():
    """测试策略筛选功能"""
    print("=" * 60)
    print("测试策略筛选功能")
    print("=" * 60)
    
    # 创建策略实例
    strategy = ConvertibleBondStrategyBatch('2024-01-01', '2024-03-31')
    
    # 检查是否有本地数据
    data_folder = Path('BatchData')
    if not data_folder.exists() or not list(data_folder.glob('*.pkl')):
        print("没有找到本地数据，请先运行数据获取测试")
        return False
    
    # 测试月末日期获取
    print("\n1. 测试月末日期获取...")
    trading_dates = strategy.load_data('trading_dates')
    if not trading_dates.empty:
        month_ends = strategy.get_month_end_dates(trading_dates)
        print(f"✅ 月末日期获取成功: {len(month_ends)} 个")
        print(f"   月末日期: {month_ends}")
        
        if month_ends:
            # 测试单日筛选
            test_date = month_ends[0]
            print(f"\n2. 测试 {test_date} 的筛选...")
            selected_bonds = strategy.screen_bonds_for_date(test_date)
            if not selected_bonds.empty:
                print(f"✅ 筛选成功: {len(selected_bonds)} 只债券")
                print(f"   选中债券: {selected_bonds['bond_code'].head(5).tolist()}")
            else:
                print("⚠️ 筛选结果为空")
    else:
        print("❌ 交易日历数据加载失败")
        return False
    
    print("\n" + "=" * 60)
    print("策略筛选测试完成")
    print("=" * 60)
    return True

def test_full_backtest():
    """测试完整回测流程"""
    print("=" * 60)
    print("测试完整回测流程")
    print("=" * 60)
    
    # 创建策略实例（短期测试）
    strategy = ConvertibleBondStrategyBatch('2024-01-01', '2024-03-31')
    
    # 运行回测
    try:
        strategy.run_backtest()
        print("✅ 完整回测流程测试成功")
        return True
    except Exception as e:
        print(f"❌ 完整回测流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试程序"""
    print("批量数据版本可转债策略测试程序")
    print("=" * 80)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试数据获取功能")
        print("2. 测试策略筛选功能")
        print("3. 测试完整回测流程")
        print("4. 运行所有测试")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            test_data_fetching()
        elif choice == '2':
            test_strategy_screening()
        elif choice == '3':
            test_full_backtest()
        elif choice == '4':
            print("运行所有测试...")
            success1 = test_data_fetching()
            if success1:
                success2 = test_strategy_screening()
                if success2:
                    test_full_backtest()
        elif choice == '5':
            print("退出测试程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
