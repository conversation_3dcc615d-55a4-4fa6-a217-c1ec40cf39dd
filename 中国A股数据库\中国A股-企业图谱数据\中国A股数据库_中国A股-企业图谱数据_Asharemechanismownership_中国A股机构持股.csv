﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(100),万得定义的内部代码，用于标识公司唯一性；万得自定义的用来识别公司的唯一编码；公司或法人组织在万得库中对应的唯一标识ID；,
S_INFO_DIMENSION,一级关系维度名称,Tier 1 relationship dimension name,VARCHAR2(100),,
S_INFO_DIMENSION1,二级关系维度名称,Secondary relationship dimension name,VARCHAR2(100),,
S_INFO_COMP_NAME,股东公司名称,Shareholder company name,VARCHAR2(100),公司注册的合法名称；公司或企业组织的名称；基金产品的公布名称；证券或公司对应的中文全称；公司的称呼；,
S_INFO_COMP_SNAME,股东公司中文简称,Shareholder Company Chinese Abbreviation,VARCHAR2(100),公司公布的中文简称；公司中文简称；公司或企业组织的中文名称缩写；公司的简化名称；公司或证券的中文简称；公司名称的中文简短称呼；,
S_INFO_COMPCODE1,股东公司ID,Shareholder company ID,VARCHAR2(100),万得自定义的股东名称对应的规范化公司ID,
S_HOLDER_ENDDATE,报告期,Reporting period,VARCHAR2(10),数据对应的截止日期情况,
S_HOLDER_PCT,持股比例,Shareholding ratio,"NUMBER(20,4)",股东持有股份占流通股本的比例,
S_HOLDER_TYPE,股东类型,Shareholder Type,VARCHAR2(40),万得自定义的股东对应的类型,
ANN_DT,公告日期,Announcement Date,VARCHAR2(8),数据来源公告的发布日期,
