﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object id,VARCHAR2(100),,
S_INFO_COMPCODE,公司ID,Company id,VARCHAR2(100),万得定义的内部代码，用于标识公司唯一性；万得自定义的用来识别公司的唯一编码；证券产品在万得库中对应的唯一标识ID；,
S_INFO_DIMENSION,一级关系维度名称,Tier 1 relationship dimension name,VARCHAR2(100),,
S_INFO_DIMENSION1,二级关系维度名称,Secondary relationship dimension name,VARCHAR2(100),,
S_INFO_COMP_NAME,担保公司名称,Name of guarantee company,VARCHAR2(100),提供担保方、担保人,
S_INFO_COMP_SNAME,担保公司中文简称,Chinese abbreviation of guarantee company,VARCHAR2(40),公司公布的中文简称；公司中文简称；公司或企业组织的中文名称缩写；公司的简化名称；公司或证券的中文简称；公司名称的中文简短称呼；,
S_INFO_COMPCODE1,担保公司ID,Guarantee company id,VARCHAR2(100),万得自定义的用来识别担保方公司的唯一编码,
REPORT_PERIOD,报告期,Reporting period,VARCHAR2(8),单次担保公布完成的时间,
S_INFO_AMOUNT,金额,Amount of money,"NUMBER(20,4)",公司提供担保总金额,万元
RLSP_GUARANTD_INFODISCLS,被担保公司与信披方关系,Relationship Between The Guaranteed Comp And The Information Disclosure Comp,VARCHAR2(40),被担保方与信息披露方的关系,
S_INFO_COMPCODE_ID,信息披露方ID,Information Discloser ID,VARCHAR2(10),万得自定义的用来识别公司的ID,
EVENT_NUMBER,担保事件序号,Guarantor Event Number,VARCHAR2(4),,
S_INFO_COMP_NAME_BGC,被担保公司名称,Guaranteed company name,VARCHAR2(200),单次担保中需他人提供担保的公司,
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期,
