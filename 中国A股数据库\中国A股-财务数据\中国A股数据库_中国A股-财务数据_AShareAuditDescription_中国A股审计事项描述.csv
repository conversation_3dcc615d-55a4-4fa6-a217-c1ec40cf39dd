﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(38),,
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(10),万得自定义的用来识别公司的唯一编码,
REPORT_PERIOD,报告期,Reporting period,VARCHAR2(8),反映企业经营成果、现金流量的特定时期根据长短不同分为年度、半年度、季度,
AUDIT_PROJECT_TYPE,审计项目类型代码,Audit item type code,"NUMBER(9,0)",可关联AShareTypeCode获取代码对应名称,
AUDIT_MATTERS_TYPE,关键审计事项类型,Key audit item type,VARCHAR2(500),关键审计事项的具体事项类型,
AUDIT_MATTERS_NUM,关键审计事项序号,Key audit matter serial number,"NUMBER(5,0)",关键审计事项的具体事项的顺序标识,
MATTER_DESCRIPTION,事项描述,Description of the matter,VARCHAR2(4000),关键审计事项的具体事项描述,
AUDIT_RESPONSE,审计应对,Audit response,VARCHAR2(4000),对该具体审计事项的审计应对,
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期,
