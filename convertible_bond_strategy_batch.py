#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可转债量化策略 - 批量数据版本
先获取全部数据保存到本地，再进行筛选和回测
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import pickle
import warnings
from typing import Dict, List, Tuple, Optional
from get_db_funs import get_db_data

warnings.filterwarnings('ignore')

class ConvertibleBondStrategyBatch:
    """可转债量化策略 - 批量数据处理版本"""
    
    def __init__(self, start_date: str = '2020-01-01', end_date: str = '2024-12-31'):
        """
        初始化策略
        
        参数:
        start_date: 策略开始日期
        end_date: 策略结束日期
        """
        self.start_date = start_date
        self.end_date = end_date
        
        # 创建数据存储目录
        self.data_folder = Path('BatchData')
        self.data_folder.mkdir(exist_ok=True)
        
        # 筛选条件
        self.min_stock_price = 4.0          # 正股收盘价 >= 4元
        self.min_bond_balance = 5.0         # 转债余额 >= 5亿元
        self.min_market_cap = 40.0          # 正股市值 >= 40亿元
        self.min_remaining_term = 1.0       # 转债剩余期限 >= 1年
        self.min_rating = 'A+'              # 转债评级 >= A+
        self.ytm_percentile = 0.8           # YTM前20%
        self.exclude_loss_stocks = True     # 排除亏损股票
        self.min_avg_amount = 10000000      # 20日均成交额 >= 1000万元
        
        print("可转债量化策略（批量数据版本）初始化完成")
        print(f"策略期间: {start_date} 至 {end_date}")
        print("筛选条件:")
        print(f"  - 正股收盘价 ≥ {self.min_stock_price}元")
        print(f"  - 转债余额 ≥ {self.min_bond_balance}亿元")
        print(f"  - 正股市值 ≥ {self.min_market_cap}亿元")
        print(f"  - 转债剩余期限 ≥ {self.min_remaining_term}年")
        print(f"  - 转债评级 ≥ {self.min_rating}")
        print(f"  - YTM排名前{int((1-self.ytm_percentile)*100)}%")
        print(f"  - 排除亏损股票" if self.exclude_loss_stocks else "  - 包含亏损股票")
        print(f"  - 20日均成交额 ≥ {self.min_avg_amount/10000}万元")
    
    def get_data_file_path(self, data_type: str) -> Path:
        """获取数据文件路径"""
        return self.data_folder / f"{data_type}_{self.start_date}_{self.end_date}.pkl"
    
    def save_data(self, data: pd.DataFrame, data_type: str):
        """保存数据到本地"""
        file_path = self.get_data_file_path(data_type)
        with open(file_path, 'wb') as f:
            pickle.dump(data, f)
        print(f"{data_type}数据已保存到: {file_path}")
    
    def load_data(self, data_type: str) -> pd.DataFrame:
        """从本地加载数据"""
        file_path = self.get_data_file_path(data_type)
        if file_path.exists():
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
            print(f"从本地加载{data_type}数据: {len(data)}条")
            return data
        else:
            print(f"本地不存在{data_type}数据文件")
            return pd.DataFrame()
    
    def fetch_all_trading_dates(self):
        """获取所有交易日历"""
        print("=" * 60)
        print("获取交易日历数据...")
        
        # 检查是否已有本地数据
        local_data = self.load_data('trading_dates')
        if not local_data.empty:
            return local_data
        
        try:
            start_date_str = self.start_date.replace('-', '')
            end_date_str = self.end_date.replace('-', '')

            conditions = {
                'S_INFO_EXCHMARKET': 'SSE',
                'TRADE_DAYS': [start_date_str, end_date_str]
            }

            trade_data = get_db_data('AShareCalendar', keywords=['TRADE_DAYS'], additional_conditions=conditions)

            if trade_data is not None and not trade_data.empty:
                # 重命名列以保持一致性
                trade_data = trade_data.rename(columns={'TRADE_DAYS': 'TRADE_DT'})
                trade_data = trade_data.sort_values('TRADE_DT').reset_index(drop=True)
                
                self.save_data(trade_data, 'trading_dates')
                print(f"获取到 {len(trade_data)} 个交易日")
                return trade_data
            else:
                print("获取交易日历失败")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取交易日历出错: {e}")
            return pd.DataFrame()
    
    def fetch_all_convertible_bonds(self):
        """获取所有可转债基础信息"""
        print("=" * 60)
        print("获取可转债基础信息...")
        
        # 检查是否已有本地数据
        local_data = self.load_data('convertible_bonds')
        if not local_data.empty:
            return local_data
        
        try:
            # 从债券基本资料表获取可转债
            keywords = ['S_INFO_WINDCODE', 'S_INFO_NAME', 'B_INFO_SPECIALBONDTYPE']
            conditions = {
                'OR': [
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%可转债%')},
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%可转换%')},
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%转债%')}
                ]
            }

            bond_data = get_db_data('CBondDescription', keywords=keywords, additional_conditions=conditions)

            if bond_data is not None and not bond_data.empty:
                # 筛选SZ和SH后缀的可转债
                pattern = r'\.(SZ|SH)$'
                mask = bond_data['S_INFO_WINDCODE'].str.contains(pattern, regex=True, na=False)
                bond_data = bond_data[mask]
                
                self.save_data(bond_data, 'convertible_bonds')
                print(f"获取到 {len(bond_data)} 只可转债基础信息")
                return bond_data
            else:
                print("获取可转债基础信息失败")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取可转债基础信息出错: {e}")
            return pd.DataFrame()
    
    def fetch_all_bond_valuation_data(self, bond_codes: List[str]):
        """获取所有可转债估值数据"""
        print("=" * 60)
        print("获取可转债估值数据...")
        
        # 检查是否已有本地数据
        local_data = self.load_data('bond_valuation')
        if not local_data.empty:
            return local_data
        
        try:
            keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'CB_ANAL_YTM', 'CB_ANAL_PTM', 'CB_ANAL_CONVPREMIUMRATIO']
            conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'TRADE_DT': [self.start_date, self.end_date]
            }
            
            valuation_data = get_db_data('CCBondValuation', keywords=keywords, additional_conditions=conditions)
            
            if valuation_data is not None and not valuation_data.empty:
                # 数据类型转换
                numeric_cols = ['CB_ANAL_YTM', 'CB_ANAL_PTM', 'CB_ANAL_CONVPREMIUMRATIO']
                for col in numeric_cols:
                    if col in valuation_data.columns:
                        valuation_data[col] = pd.to_numeric(valuation_data[col], errors='coerce')
                
                self.save_data(valuation_data, 'bond_valuation')
                print(f"获取到 {len(valuation_data)} 条可转债估值数据")
                return valuation_data
            else:
                print("获取可转债估值数据失败")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取可转债估值数据出错: {e}")
            return pd.DataFrame()
    
    def fetch_all_bond_balance_data(self, bond_codes: List[str]):
        """获取所有可转债余额数据"""
        print("=" * 60)
        print("获取可转债余额数据...")
        
        # 检查是否已有本地数据
        local_data = self.load_data('bond_balance')
        if not local_data.empty:
            return local_data
        
        try:
            keywords = ['S_INFO_WINDCODE', 'S_INFO_CHANGEDATE', 'B_INFO_OUTSTANDINGBALANCE']
            conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'S_INFO_CHANGEDATE': [self.start_date.replace('-', ''), self.end_date.replace('-', '')]
            }

            balance_data = get_db_data('CCBondAmount', keywords=keywords, additional_conditions=conditions)
            
            if balance_data is not None and not balance_data.empty:
                # 数据类型转换，万元转元
                balance_data['B_INFO_OUTSTANDINGBALANCE'] = pd.to_numeric(
                    balance_data['B_INFO_OUTSTANDINGBALANCE'], errors='coerce'
                ) * 10000  # 万元转元
                
                self.save_data(balance_data, 'bond_balance')
                print(f"获取到 {len(balance_data)} 条可转债余额数据")
                return balance_data
            else:
                print("获取可转债余额数据失败")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取可转债余额数据出错: {e}")
            return pd.DataFrame()
    
    def fetch_all_bond_rating_data(self, bond_codes: List[str]):
        """获取所有可转债评级数据"""
        print("=" * 60)
        print("获取可转债评级数据...")
        
        # 检查是否已有本地数据
        local_data = self.load_data('bond_rating')
        if not local_data.empty:
            return local_data
        
        try:
            keywords = ['S_INFO_WINDCODE', 'ANN_DT', 'B_INFO_CREDITRATING']
            conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'ANN_DT': [self.start_date, self.end_date]
            }
            
            rating_data = get_db_data('CBondRating', keywords=keywords, additional_conditions=conditions)
            
            if rating_data is not None and not rating_data.empty:
                self.save_data(rating_data, 'bond_rating')
                print(f"获取到 {len(rating_data)} 条可转债评级数据")
                return rating_data
            else:
                print("获取可转债评级数据失败")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取可转债评级数据出错: {e}")
            return pd.DataFrame()
    
    def fetch_all_bond_stock_mapping(self, bond_codes: List[str]):
        """获取所有可转债与正股的映射关系"""
        print("=" * 60)
        print("获取可转债-正股映射关系...")
        
        # 检查是否已有本地数据
        local_data = self.load_data('bond_stock_mapping')
        if not local_data.empty:
            return local_data
        
        try:
            # 第一步：获取可转债到公司代码的映射
            mapping_keywords = ['S_INFO_WINDCODE', 'S_INFO_COMPCODE']
            mapping_conditions = {'S_INFO_WINDCODE': bond_codes}

            mapping_data = get_db_data('CCBondIssuance', keywords=mapping_keywords, additional_conditions=mapping_conditions)

            if mapping_data is not None and not mapping_data.empty:
                # 第二步：获取公司代码到股票代码的映射
                comp_keywords = ['S_INFO_COMPCODE', 'S_INFO_WINDCODE']
                comp_conditions = {'S_INFO_COMPCODE': mapping_data['S_INFO_COMPCODE'].tolist()}

                comp_data = get_db_data('AShareDescription', keywords=comp_keywords, additional_conditions=comp_conditions)

                if comp_data is not None and not comp_data.empty:
                    # 创建映射字典
                    comp_to_stock = dict(zip(comp_data['S_INFO_COMPCODE'], comp_data['S_INFO_WINDCODE']))

                    # 合并映射关系
                    final_mapping = []
                    for _, row in mapping_data.iterrows():
                        bond_code = row['S_INFO_WINDCODE']
                        comp_code = row['S_INFO_COMPCODE']
                        if comp_code in comp_to_stock:
                            stock_code = comp_to_stock[comp_code]
                            final_mapping.append({
                                'S_INFO_WINDCODE': bond_code,
                                'S_INFO_UNDERLYINGWINDCODE': stock_code
                            })

                    if final_mapping:
                        final_data = pd.DataFrame(final_mapping)
                        self.save_data(final_data, 'bond_stock_mapping')
                        print(f"获取到 {len(final_data)} 个可转债-正股映射关系")
                        return final_data
                    else:
                        print("没有找到有效的映射关系")
                        return pd.DataFrame()
                else:
                    print("获取公司代码到股票代码映射失败")
                    return pd.DataFrame()
            else:
                print("获取可转债到公司代码映射失败")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取可转债-正股映射关系出错: {e}")
            return pd.DataFrame()
    
    def fetch_all_data(self):
        """批量获取所有需要的数据"""
        print("=" * 80)
        print("开始批量获取所有数据...")
        print("=" * 80)
        
        # 1. 获取交易日历
        trading_dates = self.fetch_all_trading_dates()
        if trading_dates.empty:
            print("获取交易日历失败，无法继续")
            return False
        
        # 2. 获取可转债基础信息
        bond_info = self.fetch_all_convertible_bonds()
        if bond_info.empty:
            print("获取可转债基础信息失败，无法继续")
            return False
        
        bond_codes = bond_info['S_INFO_WINDCODE'].tolist()
        print(f"将获取 {len(bond_codes)} 只可转债的相关数据")
        
        # 3. 获取可转债估值数据
        self.fetch_all_bond_valuation_data(bond_codes)
        
        # 4. 获取可转债余额数据
        self.fetch_all_bond_balance_data(bond_codes)
        
        # 5. 获取可转债评级数据
        self.fetch_all_bond_rating_data(bond_codes)
        
        # 6. 获取可转债-正股映射关系
        mapping_data = self.fetch_all_bond_stock_mapping(bond_codes)
        
        if not mapping_data.empty:
            stock_codes = mapping_data['S_INFO_UNDERLYINGWINDCODE'].unique().tolist()
            print(f"将获取 {len(stock_codes)} 只正股的相关数据")
            
            # 7. 获取正股价格数据
            self.fetch_all_stock_price_data(stock_codes)
            
            # 8. 获取正股市值数据
            self.fetch_all_stock_market_cap_data(stock_codes)
            
            # 9. 获取正股财务数据
            self.fetch_all_stock_financial_data(stock_codes)
        
        # 10. 获取可转债价格数据
        self.fetch_all_bond_price_data(bond_codes)
        
        print("=" * 80)
        print("所有数据获取完成！")
        print("=" * 80)
        return True

    def fetch_all_stock_price_data(self, stock_codes: List[str]):
        """获取所有正股价格数据"""
        print("=" * 60)
        print("获取正股价格数据...")

        # 检查是否已有本地数据
        local_data = self.load_data('stock_prices')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE', 'S_DQ_AMOUNT']
            conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'TRADE_DT': [self.start_date, self.end_date]
            }

            price_data = get_db_data('AShareEODPrices', keywords=keywords, additional_conditions=conditions)

            if price_data is not None and not price_data.empty:
                # 数据类型转换
                price_data['S_DQ_CLOSE'] = pd.to_numeric(price_data['S_DQ_CLOSE'], errors='coerce')
                price_data['S_DQ_AMOUNT'] = pd.to_numeric(price_data['S_DQ_AMOUNT'], errors='coerce') * 1000  # 千元转元

                self.save_data(price_data, 'stock_prices')
                print(f"获取到 {len(price_data)} 条正股价格数据")
                return price_data
            else:
                print("获取正股价格数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取正股价格数据出错: {e}")
            return pd.DataFrame()

    def fetch_all_stock_market_cap_data(self, stock_codes: List[str]):
        """获取所有正股市值数据"""
        print("=" * 60)
        print("获取正股市值数据...")

        # 检查是否已有本地数据
        local_data = self.load_data('stock_market_cap')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_VAL_MV']
            conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'TRADE_DT': [self.start_date.replace('-', ''), self.end_date.replace('-', '')]
            }

            market_cap_data = get_db_data('AShareEODDerivativeIndicator', keywords=keywords, additional_conditions=conditions)

            if market_cap_data is not None and not market_cap_data.empty:
                # 数据类型转换，万元转亿元
                market_cap_data['S_VAL_MV'] = pd.to_numeric(market_cap_data['S_VAL_MV'], errors='coerce') / 10000

                self.save_data(market_cap_data, 'stock_market_cap')
                print(f"获取到 {len(market_cap_data)} 条正股市值数据")
                return market_cap_data
            else:
                print("获取正股市值数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取正股市值数据出错: {e}")
            return pd.DataFrame()

    def fetch_all_stock_financial_data(self, stock_codes: List[str]):
        """获取所有正股财务数据"""
        print("=" * 60)
        print("获取正股财务数据...")

        # 检查是否已有本地数据
        local_data = self.load_data('stock_financial')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'ANN_DT', 'NET_PROFIT_EXCL_MIN_INT_INC']
            conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'ANN_DT': [self.start_date, self.end_date]
            }

            financial_data = get_db_data('AShareIncome', keywords=keywords, additional_conditions=conditions)

            if financial_data is not None and not financial_data.empty:
                # 数据类型转换
                financial_data['NET_PROFIT_EXCL_MIN_INT_INC'] = pd.to_numeric(
                    financial_data['NET_PROFIT_EXCL_MIN_INT_INC'], errors='coerce'
                )

                self.save_data(financial_data, 'stock_financial')
                print(f"获取到 {len(financial_data)} 条正股财务数据")
                return financial_data
            else:
                print("获取正股财务数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取正股财务数据出错: {e}")
            return pd.DataFrame()

    def fetch_all_bond_price_data(self, bond_codes: List[str]):
        """获取所有可转债价格数据"""
        print("=" * 60)
        print("获取可转债价格数据...")

        # 检查是否已有本地数据
        local_data = self.load_data('bond_prices')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE']
            conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'TRADE_DT': [self.start_date, self.end_date]
            }

            price_data = get_db_data('CBondEODPrices', keywords=keywords, additional_conditions=conditions)

            if price_data is not None and not price_data.empty:
                # 数据类型转换
                price_data['S_DQ_CLOSE'] = pd.to_numeric(price_data['S_DQ_CLOSE'], errors='coerce')

                self.save_data(price_data, 'bond_prices')
                print(f"获取到 {len(price_data)} 条可转债价格数据")
                return price_data
            else:
                print("获取可转债价格数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取可转债价格数据出错: {e}")
            return pd.DataFrame()

    def get_month_end_dates(self, trading_dates: pd.DataFrame) -> List[str]:
        """获取月末交易日"""
        try:
            dates = pd.to_datetime(trading_dates['TRADE_DT'])
            month_ends = []

            for year in range(int(self.start_date[:4]), int(self.end_date[:4]) + 1):
                for month in range(1, 13):
                    # 获取该月的所有交易日
                    month_dates = dates[
                        (dates.dt.year == year) & (dates.dt.month == month)
                    ]

                    if not month_dates.empty:
                        # 取该月最后一个交易日
                        last_date = month_dates.max().strftime('%Y%m%d')
                        if self.start_date <= last_date <= self.end_date:
                            month_ends.append(last_date)

            return sorted(month_ends)

        except Exception as e:
            print(f"获取月末交易日出错: {e}")
            return []

    def screen_bonds_for_date(self, date: str) -> pd.DataFrame:
        """对指定日期进行可转债筛选"""
        print(f"\n=== 筛选日期: {date} ===")

        # 加载所有需要的数据
        bond_valuation = self.load_data('bond_valuation')
        bond_balance = self.load_data('bond_balance')
        bond_rating = self.load_data('bond_rating')
        bond_stock_mapping = self.load_data('bond_stock_mapping')
        stock_prices = self.load_data('stock_prices')
        stock_market_cap = self.load_data('stock_market_cap')
        stock_financial = self.load_data('stock_financial')

        if any(df.empty for df in [bond_valuation, bond_balance, bond_rating,
                                  bond_stock_mapping, stock_prices, stock_market_cap]):
            print("缺少必要的数据文件")
            return pd.DataFrame()

        # 1. 获取当日的可转债估值数据
        date_valuation = bond_valuation[bond_valuation['TRADE_DT'] == date].copy()
        if date_valuation.empty:
            print(f"没有找到 {date} 的可转债估值数据")
            return pd.DataFrame()

        print(f"找到 {len(date_valuation)} 条可转债估值数据")
        print(f"估值数据字段: {list(date_valuation.columns)}")

        # 2. 获取最新的余额数据
        bond_balance['S_INFO_CHANGEDATE'] = pd.to_datetime(bond_balance['S_INFO_CHANGEDATE'])
        latest_balance = bond_balance[
            bond_balance['S_INFO_CHANGEDATE'] <= pd.to_datetime(date)
        ].groupby('S_INFO_WINDCODE').last().reset_index()

        # 3. 获取最新的评级数据
        bond_rating['ANN_DT'] = pd.to_datetime(bond_rating['ANN_DT'])
        latest_rating = bond_rating[
            bond_rating['ANN_DT'] <= pd.to_datetime(date)
        ].groupby('S_INFO_WINDCODE').last().reset_index()

        # 4. 合并可转债数据
        merged_data = date_valuation.merge(
            latest_balance[['S_INFO_WINDCODE', 'B_INFO_OUTSTANDINGBALANCE']],
            on='S_INFO_WINDCODE', how='left'
        )

        merged_data = merged_data.merge(
            latest_rating[['S_INFO_WINDCODE', 'B_INFO_CREDITRATING']],
            on='S_INFO_WINDCODE', how='left'
        )

        merged_data = merged_data.merge(
            bond_stock_mapping[['S_INFO_WINDCODE', 'S_INFO_UNDERLYINGWINDCODE']],
            on='S_INFO_WINDCODE', how='left'
        )

        print(f"合并后得到 {len(merged_data)} 条可转债数据")

        # 5. 获取正股数据
        date_stock_prices = stock_prices[stock_prices['TRADE_DT'] == date].copy()
        date_market_cap = stock_market_cap[stock_market_cap['TRADE_DT'] == date].copy()

        # 获取最新的财务数据
        stock_financial['ANN_DT'] = pd.to_datetime(stock_financial['ANN_DT'])
        latest_financial = stock_financial[
            stock_financial['ANN_DT'] <= pd.to_datetime(date)
        ].groupby('S_INFO_WINDCODE').last().reset_index()

        # 6. 合并正股数据
        stock_data = date_stock_prices.merge(
            date_market_cap[['S_INFO_WINDCODE', 'S_VAL_MV']],
            on='S_INFO_WINDCODE', how='left'
        )

        stock_data = stock_data.merge(
            latest_financial[['S_INFO_WINDCODE', 'NET_PROFIT_EXCL_MIN_INT_INC']],
            on='S_INFO_WINDCODE', how='left'
        )

        # 7. 合并可转债和正股数据
        final_data = merged_data.merge(
            stock_data,
            left_on='S_INFO_UNDERLYINGWINDCODE',
            right_on='S_INFO_WINDCODE',
            how='left',
            suffixes=('_bond', '_stock')
        )

        print(f"最终合并得到 {len(final_data)} 条数据")
        print(f"最终数据字段: {list(final_data.columns)}")

        # 8. 应用筛选条件
        return self.apply_screening_conditions(final_data, date)

    def apply_screening_conditions(self, data: pd.DataFrame, date: str) -> pd.DataFrame:
        """应用筛选条件"""
        if data.empty:
            return data

        original_count = len(data)
        print(f"开始筛选，原始数据: {original_count} 条")

        # 1. 正股收盘价 >= 4元
        data = data[data['S_DQ_CLOSE'] >= self.min_stock_price]
        print(f"正股价格筛选后: {len(data)} 条")

        # 2. 转债余额 >= 5亿元
        data = data[data['B_INFO_OUTSTANDINGBALANCE'] >= self.min_bond_balance]
        print(f"转债余额筛选后: {len(data)} 条")

        # 3. 正股市值 >= 40亿元
        data = data[data['S_VAL_MV'] >= self.min_market_cap]
        print(f"正股市值筛选后: {len(data)} 条")

        # 4. 排除亏损股票
        if self.exclude_loss_stocks:
            # 处理空值，将空值视为不亏损（保守处理）
            profit_condition = (data['NET_PROFIT_EXCL_MIN_INT_INC'].fillna(1) > 0)
            data = data[profit_condition]
            print(f"排除亏损股票后: {len(data)} 条")

        # 5. 转债评级 >= A+
        rating_order = ['AAA', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-', 'BBB+', 'BBB', 'BBB-']
        min_rating_index = rating_order.index(self.min_rating) if self.min_rating in rating_order else len(rating_order)

        def is_rating_qualified(rating):
            if pd.isna(rating) or rating == '':
                return False
            try:
                return rating_order.index(rating) <= min_rating_index
            except ValueError:
                return False

        data = data[data['B_INFO_CREDITRATING'].apply(is_rating_qualified)]
        print(f"评级筛选后: {len(data)} 条")

        # 6. YTM前20%
        if 'CB_ANAL_YTM' in data.columns:
            data = data.dropna(subset=['CB_ANAL_YTM'])
            if not data.empty:
                ytm_threshold = data['CB_ANAL_YTM'].quantile(self.ytm_percentile)
                data = data[data['CB_ANAL_YTM'] >= ytm_threshold]
                print(f"YTM筛选后: {len(data)} 条")
            else:
                print("YTM筛选后: 0 条 (无有效YTM数据)")
        else:
            print("警告: CB_ANAL_YTM字段不存在，跳过YTM筛选")

        # 7. 计算20日均成交额（这里简化处理，使用当日成交额）
        data = data[data['S_DQ_AMOUNT'] >= self.min_avg_amount]
        print(f"成交额筛选后: {len(data)} 条")

        print(f"筛选完成，从 {original_count} 条筛选出 {len(data)} 条")
        return data[['S_INFO_WINDCODE_bond', 'CB_ANAL_YTM']].rename(columns={'S_INFO_WINDCODE_bond': 'bond_code'})

    def run_strategy(self):
        """运行策略"""
        print("=" * 80)
        print("开始运行批量数据版本的可转债策略...")
        print("=" * 80)

        # 1. 加载交易日历
        trading_dates = self.load_data('trading_dates')
        if trading_dates.empty:
            print("请先运行 fetch_all_data() 获取数据")
            return pd.DataFrame(), pd.DataFrame()

        # 2. 获取月末交易日
        month_end_dates = self.get_month_end_dates(trading_dates)
        print(f"获取到 {len(month_end_dates)} 个月末交易日")

        if not month_end_dates:
            print("没有找到月末交易日")
            return pd.DataFrame(), pd.DataFrame()

        # 3. 对每个月末进行筛选
        all_selections = {}
        for date in month_end_dates:
            selected_bonds = self.screen_bonds_for_date(date)
            if not selected_bonds.empty:
                all_selections[date] = selected_bonds['bond_code'].tolist()
            else:
                all_selections[date] = []

        # 4. 构建持仓矩阵
        position_data = self.build_position_matrix(all_selections, trading_dates)

        # 5. 获取价格数据
        price_data = self.build_price_matrix(position_data)

        return price_data, position_data

    def build_position_matrix(self, selections: Dict[str, List[str]], trading_dates: pd.DataFrame) -> pd.DataFrame:
        """构建持仓矩阵"""
        print("\n构建持仓矩阵...")

        # 获取所有选中的债券
        all_bonds = set()
        for bonds in selections.values():
            all_bonds.update(bonds)
        all_bonds = sorted(list(all_bonds))

        if not all_bonds:
            print("没有选中任何债券")
            return pd.DataFrame()

        print(f"总共涉及 {len(all_bonds)} 只可转债")

        # 创建持仓矩阵
        dates = pd.to_datetime(trading_dates['TRADE_DT'])
        position_matrix = pd.DataFrame(0.0, index=dates, columns=all_bonds)

        # 填充持仓权重
        for rebalance_date, selected_bonds in selections.items():
            if selected_bonds:
                # 等权重分配
                weight = 1.0 / len(selected_bonds)

                # 找到调仓日期之后的所有交易日，直到下一个调仓日
                rebalance_dt = pd.to_datetime(rebalance_date)

                # 找到下一个调仓日
                next_rebalance_dates = [d for d in selections.keys() if d > rebalance_date]
                if next_rebalance_dates:
                    next_rebalance_dt = pd.to_datetime(min(next_rebalance_dates))
                    mask = (position_matrix.index >= rebalance_dt) & (position_matrix.index < next_rebalance_dt)
                else:
                    mask = position_matrix.index >= rebalance_dt

                # 设置持仓权重
                for bond in selected_bonds:
                    if bond in position_matrix.columns:
                        position_matrix.loc[mask, bond] = weight

        print(f"持仓矩阵构建完成，形状: {position_matrix.shape}")
        return position_matrix

    def build_price_matrix(self, position_data: pd.DataFrame) -> pd.DataFrame:
        """构建价格矩阵"""
        print("\n构建价格矩阵...")

        if position_data.empty:
            return pd.DataFrame()

        # 加载可转债价格数据
        bond_prices = self.load_data('bond_prices')
        if bond_prices.empty:
            print("没有可转债价格数据")
            return pd.DataFrame()

        # 获取需要的债券和日期
        needed_bonds = position_data.columns.tolist()
        needed_dates = position_data.index.strftime('%Y%m%d').tolist()

        # 筛选相关的价格数据
        relevant_prices = bond_prices[
            (bond_prices['S_INFO_WINDCODE'].isin(needed_bonds)) &
            (bond_prices['TRADE_DT'].isin(needed_dates))
        ].copy()

        print(f"获取到 {len(relevant_prices)} 条价格数据")

        # 构建价格矩阵
        price_matrix = relevant_prices.pivot(
            index='TRADE_DT',
            columns='S_INFO_WINDCODE',
            values='S_DQ_CLOSE'
        )

        # 重新索引以匹配持仓矩阵
        price_matrix.index = pd.to_datetime(price_matrix.index)
        price_matrix = price_matrix.reindex(index=position_data.index, columns=position_data.columns)

        # 前向填充缺失价格
        price_matrix = price_matrix.fillna(method='ffill')

        print(f"价格矩阵构建完成，形状: {price_matrix.shape}")
        print(f"缺失数据点: {price_matrix.isna().sum().sum()}")

        return price_matrix

    def calculate_strategy_returns(self, price_data: pd.DataFrame, position_data: pd.DataFrame) -> pd.DataFrame:
        """计算策略收益率"""
        print("\n计算策略收益率...")

        if price_data.empty or position_data.empty:
            return pd.DataFrame()

        # 计算日收益率
        returns = price_data.pct_change().fillna(0)

        # 计算策略日收益率
        strategy_returns = (returns * position_data.shift(1)).sum(axis=1)
        strategy_returns.iloc[0] = 0  # 第一天收益率为0

        # 计算累计收益率和净值
        cumulative_returns = (1 + strategy_returns).cumprod()

        # 构建结果DataFrame
        results_df = pd.DataFrame({
            'date': strategy_returns.index,
            'daily_return': strategy_returns.values,
            'cumulative_return': cumulative_returns.values,
            'net_value': cumulative_returns.values
        })

        print(f"策略收益率计算完成，数据点: {len(results_df)}")
        return results_df

    def save_results(self, returns_df: pd.DataFrame, position_data: pd.DataFrame) -> Tuple[Path, Path]:
        """保存结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存收益率数据
        returns_file = Path('Results') / f'批量数据转债策略收益率_{timestamp}.xlsx'
        returns_file.parent.mkdir(exist_ok=True)
        returns_df.to_excel(returns_file, index=False)

        # 保存持仓数据
        position_file = Path('Results') / f'批量数据转债策略持仓_{timestamp}.xlsx'
        position_data.to_excel(position_file)

        print(f"\n结果已保存:")
        print(f"收益率数据: {returns_file}")
        print(f"持仓数据: {position_file}")

        return returns_file, position_file

    def run_backtest(self):
        """运行完整的回测流程"""
        print("=" * 80)
        print("开始运行批量数据版本的可转债策略回测")
        print("=" * 80)

        try:
            # 1. 检查是否已有数据，如果没有则获取
            if not self.get_data_file_path('trading_dates').exists():
                print("本地数据不存在，开始获取数据...")
                success = self.fetch_all_data()
                if not success:
                    print("数据获取失败，无法进行回测")
                    return
            else:
                print("使用本地已有数据进行回测...")

            # 2. 运行策略
            price_data, position_data = self.run_strategy()

            if price_data.empty or position_data.empty:
                print("策略运行失败，无法进行回测")
                return

            # 3. 计算策略收益率
            returns_df = self.calculate_strategy_returns(price_data, position_data)

            # 4. 保存结果
            returns_file, position_file = self.save_results(returns_df, position_data)

            # 5. 显示基本统计
            if not returns_df.empty:
                final_nav = returns_df['net_value'].iloc[-1]
                total_return = (final_nav - 1) * 100

                print(f"\n=== 策略表现 ===")
                print(f"回测期间: {self.start_date} 至 {self.end_date}")
                print(f"最终净值: {final_nav:.4f}")
                print(f"累计收益率: {total_return:.2f}%")

                if len(returns_df) > 1:
                    daily_returns = returns_df['daily_return'].dropna()
                    if len(daily_returns) > 0:
                        annual_return = daily_returns.mean() * 252 * 100
                        volatility = daily_returns.std() * np.sqrt(252) * 100
                        sharpe = annual_return / volatility if volatility > 0 else 0

                        print(f"年化收益率: {annual_return:.2f}%")
                        print(f"年化波动率: {volatility:.2f}%")
                        print(f"夏普比率: {sharpe:.2f}")

            print("\n批量数据版本策略回测完成！")

        except Exception as e:
            print(f"回测过程中出现错误: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主程序"""
    print("可转债量化策略 - 批量数据版本")
    print("=" * 60)

    while True:
        print("\n请选择操作:")
        print("1. 获取所有数据（首次运行必选）")
        print("2. 运行短期回测（2024年1-3月）")
        print("3. 运行中期回测（2024年全年）")
        print("4. 运行长期回测（2020-2024年）")
        print("5. 自定义时间段回测")
        print("6. 查看本地数据状态")
        print("7. 退出")

        choice = input("\n请输入选择 (1-7): ").strip()

        if choice == '1':
            print("\n" + "=" * 60)
            print("开始获取所有数据...")
            start_date = input("请输入开始日期 (YYYY-MM-DD, 默认2020-01-01): ").strip() or '2020-01-01'
            end_date = input("请输入结束日期 (YYYY-MM-DD, 默认2024-12-31): ").strip() or '2024-12-31'

            strategy = ConvertibleBondStrategyBatch(start_date, end_date)
            strategy.fetch_all_data()

        elif choice == '2':
            print("\n" + "=" * 60)
            print("运行短期回测（2024年1-3月）")
            strategy = ConvertibleBondStrategyBatch('2024-01-01', '2024-03-31')
            strategy.run_backtest()

        elif choice == '3':
            print("\n" + "=" * 60)
            print("运行中期回测（2024年全年）")
            strategy = ConvertibleBondStrategyBatch('2024-01-01', '2024-12-31')
            strategy.run_backtest()

        elif choice == '4':
            print("\n" + "=" * 60)
            print("运行长期回测（2020-2024年）")
            strategy = ConvertibleBondStrategyBatch('2020-01-01', '2024-12-31')
            strategy.run_backtest()

        elif choice == '5':
            print("\n" + "=" * 60)
            print("自定义时间段回测")
            start_date = input("请输入开始日期 (YYYY-MM-DD): ").strip()
            end_date = input("请输入结束日期 (YYYY-MM-DD): ").strip()

            if start_date and end_date:
                strategy = ConvertibleBondStrategyBatch(start_date, end_date)
                strategy.run_backtest()
            else:
                print("日期输入无效")

        elif choice == '6':
            print("\n" + "=" * 60)
            print("本地数据状态:")
            data_folder = Path('BatchData')
            if data_folder.exists():
                files = list(data_folder.glob('*.pkl'))
                if files:
                    print(f"找到 {len(files)} 个数据文件:")
                    for file in sorted(files):
                        size_mb = file.stat().st_size / (1024 * 1024)
                        print(f"  - {file.name} ({size_mb:.1f} MB)")
                else:
                    print("没有找到数据文件")
            else:
                print("数据目录不存在")

        elif choice == '7':
            print("退出程序")
            break

        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
