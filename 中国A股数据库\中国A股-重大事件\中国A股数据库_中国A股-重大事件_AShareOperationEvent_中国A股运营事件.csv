﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
COMP_ID,公司id,Company Id,VARCHAR2(10),万得定义的内部代码，用于标识公司唯一性；万得自定义的用来识别公司的唯一编码；公司或法人组织在万得库中对应的唯一标识ID；,
SEC_ID,品种ID,Variety ID,VARCHAR2(10),万得自定义的用于标识证券唯一性的内部代码；公司或债券代码；,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
ANN_DATE,公告日期,Announcement date,VARCHAR2(8),公告发布日期；公告发布的当天的日期；,
EVENT_TYPE,事件类型,Event type,"NUMBER(9,0)",该公司运营事件的类型；用代码记录事件的类型；,
EVENT_NAME,事件标题,Event title,VARCHAR2(500),上市公司披露的公告标题；记录事件发生的标题；,
CATTYPE_ID,品种类别代码,Variety category code,"NUMBER(9,0)","********* 证券
********* 公司
********* 个人",
