---
type: "always_apply"
description: "可转债"
---
1.转债标的是：cb_csv_file = 'SZ_SH_可转债代码简称.csv'
cb_info = pd.read_csv(cb_csv_file, encoding='utf-8')
2.转债要再进行一次映射，获取交易所上市的正股代码。
windcode_table = 'windcustomcode'
windcode_keys = ['S_INFO_WINDCODE', 'S_INFO_COMPCODE', 'S_INFO_SECURITIESTYPES']
windcode_conditions = {
            'AND': [
                {'S_INFO_COMPCODE': comp_codes},
                {'S_INFO_SECURITIESTYPES': 'A'}
            ]
        }
        
windcode_data = get_db_data(windcode_table, keywords=windcode_keys, additional_conditions=windcode_conditions)
comp_to_stock = dict(zip(stock_desc_data['S_INFO_COMPCODE'], stock_desc_data['S_INFO_WINDCODE']))
cb_info['STOCK_WINDCODE'] = cb_info['S_INFO_COMPCODE'].map(comp_to_stock)