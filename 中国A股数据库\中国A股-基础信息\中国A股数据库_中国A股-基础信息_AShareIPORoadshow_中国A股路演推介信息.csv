﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(38),,
COMP_ID,公司ID,Company ID,VARCHAR2(10),万得自定义的用来识别公司的ID,
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期,
ROADSHOW_TYPE,路演类型代码,Roadshow type code,"NUMBER(9,0)",万得自定义的用来识别路演内容的唯一编码,
ROADSHOW_MODE,路演方式代码,Roadshow mode code,"NUMBER(9,0)",万得自定义的用来识别开展路演的方式的唯一编码,
ROADSHOW_DATE,路演日期,Roadshow date,VARCHAR2(8),举行路演的日期,
START_TM,路演开始时间,Roadshow start time,VARCHAR2(8),路演的开始时间,
END_TM,路演截止时间,Roadshow end time,VARCHAR2(8),路演的结束时间,
CITY,路演城市,Roadshow city,VARCHAR2(10),万得自定义的用来识别推介方式类型的编码,
PLACE,路演地点,Roadshow place,VARCHAR2(500),举行路演的具体会议室/网址,
ADDRESS,路演地址,Roadshow address,VARCHAR2(500),举行路演的地址,
