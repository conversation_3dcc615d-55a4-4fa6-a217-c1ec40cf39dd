﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(38),,
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(10),万得自定义的用来识别公司的唯一编码,
ANN_DT,日期,Date,VARCHAR2(8),记录各次该股票盈利预测变更（即数据发生变动：该股票盈利预测1571表有新的数据或者周期内该股票有记录超过周期，不在180天内，发生失效）以及基准年度变更（快报或年报的公布日）的日期,
EST_REPORT_DT,报告期,Reporting period,VARCHAR2(8),预测数据对应的会计期间，大于等于未公布年报的会计期间,
PREDICTION_NUMBER,预测家数,Number of forecasters,"NUMBER(20,0)",以“T日”向前寻找180天内各机构对某公司某报告期最新一次预测，计算机构家数,
EST_EPS,预测每股收益平均值,Earnings per share,"NUMBER(20,4)",预测净利润平均值/预测基准股本综合值，即F7_1683/ F90_1914,
EST_OPER_REVENUE,预测主营业务收入平均值,Main business income,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的主营业务收入F7_1571（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算算术平均值,万元
NET_PROFIT,预测净利润平均值,Net profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净利润F8_1571（该记录日期需在180天范围内），若F8_1571为空，则取F34_1571，且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算算术平均值,万元
EST_EPS_MAX,预测每股收益最大值,Maximum earnings per share,"NUMBER(20,4)",预测净利润最大值/预测基准股本综合值，即F15_1683/ F90_1914,元
EST_EPS_MIN,预测每股收益最小值,Minimum earnings per share,"NUMBER(20,4)",预测净利润最小值/预测基准股本综合值，即F16_1683/ F90_1914,元
EST_OPER_REVENUE_MAX,预测主营业务收入最大值,Main business income maximum,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的主营业务收入F7_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最大值,万元
EST_OPER_REVENUE_MIN,预测主营业务收入最小值,Main business income minimum,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的主营业务收入F7_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最小值,万元
EST_OPER_REVENUE_RAISED,主营业务收入调高家数,Increase in the income of the main business (Compared with a month ago),"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的主营业务收入F7_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），再以“T减180天”向前寻找相应机构的最新一次主营业务收入预测值F7_1571，且综合值计算标记为1，T日180内机构预测值减去（T减180天）周期外机构预测值，若差值>0，计数1，统计调高家数合计（机构不重复）,
EST_OPER_REVENUE_DOWN,主营业务收入调低家数,Main business income downgrade (Compared with a month ago),"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的主营业务收入F7_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），再以“T减180天”向前寻找相应机构的最新一次主营业务收入预测值F7_1571，且综合值计算标记为1，T日180内机构预测值减去（T减180天）周期外机构预测值，若差值<0，计数1，统计调低家数合计（机构不重复）,
EST_OPER_REVENUE_MAINTAIN,主营业务收入维持家数,Main business income maintained by number of households (Compared with a month ago),"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的主营业务收入F7_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），再以“T减180天”向前寻找相应机构的最新一次主营业务收入预测值F7_1571，且综合值计算标记为1，T日180内机构预测值减去（T减180天）周期外机构预测值，若差值=0，计数1，统计维持家数合计（机构不重复）,
NET_PROFIT_MAX,预测净利润最大值,Maximum net profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净利润F8_1571（该记录需日期在180天范围内），若F8_1571为空，则取F34_1571，且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最大值,万元
NET_PROFIT_MIN,预测净利润最小值,Net profit minimum,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净利润F8_1571（该记录需日期在180天范围内），若F8_1571为空，则取F34_1571，且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最小值,万元
NET_PROFIT_RAISED,净利润调高家数,Increase in net profit (Compared with a month ago),"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净利润F8_1571（该记录需日期在180天范围内），若F8_1571为空，则取F34_1571，且综合值计算标记为1（是否参与一致预测计算F21_1571=1），再以“T减180天”向前寻找相应机构的最新一次净利润F8_1571，若F8_1571为空，则取F34_1571，且综合值计算标记为1，T日180天内机构预测值减去（T减180天）周期外机构预测值，若差值>0，计数1，统计调高家数合计（机构不重复）,
NET_PROFIT_DOWN,净利润调低家数,Net profit downturn (Compared with a month ago),"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净利润F8_1571（该记录需日期在180天范围内），若F8_1571为空，则取F34_1571，且综合值计算标记为1（是否参与一致预测计算F21_1571=1），再以“T减180天”向前寻找相应机构的最新一次净利润F8_1571，若F8_1571为空，则取F34_1571，且综合值计算标记为1，T日180天内机构预测值减去（T减180天）周期外机构预测值，若差值<0，计数1，统计调低家数合计（机构不重复）,
NET_PROFIT_MAINTAIN,净利润维持家数,Net profit to maintain the number of households (Compared with a month ago),"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净利润F8_1571（该记录需日期在180天范围内），若F8_1571为空，则取F34_1571，且综合值计算标记为1（是否参与一致预测计算F21_1571=1），再以“T减180天”向前寻找相应机构的最新一次净利润F8_1571，若F8_1571为空，则取F34_1571，且综合值计算标记为1，T日180天内机构预测值减去（T减180天）周期外机构预测值，若差值=0，计数1，统计维持家数合计（机构不重复）,
EST_EPS_MEDIAN,预测每股收益中值,Median earnings per share,"NUMBER(20,4)",预测净利润中值/预测基准股本综合值，即F24_1683/ F90_1914,元
EST_EPS_DIFFERENCE,预测每股收益标准差,Standard deviation of earnings per share,"NUMBER(20,4)",预测净利润标准差/预测基准股本综合值，即F25_1683/ F90_1914,元
EST_OPER_REVENUE_MEDIAN,预测主营业务收入中值,Median income from main operations,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的主营业务收入F7_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取中值,万元
EST_OPER_REVENUE_DIF,预测主营业务收入标准差,Main business income standard deviation,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的主营业务收入F7_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算标准差,万元
NET_PROFIT_MEDIAN,预测净利润中值,Median net profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净利润F8_1571（该记录需日期在180天范围内），若F8_1571为空，则取F34_1571，且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取中值,万元
NET_PROFIT_DIF,预测净利润标准差,Net profit standard deviation,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净利润F8_1571（该记录需日期在180天范围内），若F8_1571为空，则取F34_1571，且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算标准差,万元
EST_CFPS,预测每股现金流平均值,Cash flow per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股现金流F12_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总现金流值，再用总现金流值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股现金流，计算每股现金流平均值,元
EST_CFPS_MEDIAN,预测每股现金流中值,Median cash flow per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股现金流F12_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总现金流值，再用总现金流值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股现金流，取中值,元
EST_CFPS_DIF,预测每股现金流(CPS)标准差,Standard deviation of cash flow per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股现金流F12_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总现金流值，再用总现金流值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股现金流，计算标准差,元
EST_CFPS_MAX,预测每股现金流最大值,Maximum cash flow per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股现金流F12_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总现金流值，再用总现金流值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股现金流，取最大值,元
EST_CFPS_MIN,预测每股现金流最小值,Minimum cash flow per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股现金流F12_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总现金流值，再用总现金流值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股现金流，取最小值,元
EST_DPS,预测每股股利平均值,Dividend per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股股利F13_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总股利值，再用总股利值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股股利，计算每股股利平均值,元
EST_DPS_MEDIAN,预测每股股利中值,Median dividend per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股股利F13_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总股利值，再用总股利值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股股利，取中值,元
EST_DPS_DIF,预测每股股利标准差,Dividend standard per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股股利F13_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总股利值，再用总股利值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股股利，计算标准差,元
EST_DPS_MAX,预测每股股利最大值,Maximum dividend per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股股利F13_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总股利值，再用总股利值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股股利，取最大值,元
EST_DPS_MIN,预测每股股利最小值,Minimum dividend per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股股利F13_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），得到总股利值，再用总股利值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股股利，取最小值,元
EST_EBIT,预测息税前利润平均值,EBIT,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税前利润F14_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算平均值,万元
EST_EBIT_MEDIAN,预测息税前利润中值,EBIT Median,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税前利润F14_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取中值,万元
EST_EBIT_DIF,预测息税前利润标准差,EBIT margin,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税前利润F14_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算标准差,万元
EST_EBIT_MAX,预测息税前利润最大值,EBIT Maximum,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税前利润F14_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最大值,万元
EST_EBIT_MIN,预测息税前利润最小值,EBIT Minimum,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税前利润F14_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最小值,万元
EST_EBITDA,预测息税折旧摊销前利润平均值,EBITDA,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税折旧摊销前利润F15_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算平均值,万元
EST_EBITDA_MEDIAN,预测息税折旧摊销前利润中值,EBITDA Median,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税折旧摊销前利润F15_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取中值,万元
EST_EBITDA_DIF,预测息税折旧摊销前利润标准差,EBITDA Standard deviation,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税折旧摊销前利润F15_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算标准差,万元
EST_EBITDA_MAX,预测息税折旧摊销前利润最大值,EBITDA Maximum,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税折旧摊销前利润F15_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最大值,万元
EST_EBITDA_MIN,预测息税折旧摊销前利润最小值,EBITDA Minimum value,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的息税折旧摊销前利润F15_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最小值,万元
EST_BPS,预测每股净资产平均值,Net assets per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股净资产F16_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），得到总净资产值，再用总净资产值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股净资产，计算每股净资产平均值,元
EST_BPS_MEDIAN,预测每股净资产中值,Median net asset value per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股净资产F16_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），得到总净资产值，再用总净资产值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股净资产，取中值,元
EST_BPS_DIF,预测每股净资产标准差,Standard deviation of net assets per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股净资产F16_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），得到总净资产值，再用总净资产值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股净资产，计算标准差,元
EST_BPS_MAX,预测每股净资产最大值,Maximum net assets per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股净资产F16_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），得到总净资产值，再用总净资产值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股净资产，取最大值,元
EST_BPS_MIN,预测每股净资产最小值,Minimum net asset value per share,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的每股净资产F16_1571*预测基准股本（万股）F23_1571（该记录需日期在180天范围内），得到总净资产值，再用总净资产值/预测基准股本综合值F90_1914，得到各个机构的一致预测每股净资产，取最小值,元
EST_TOTAL_PROFIT,预测利润总额平均值,Total profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的利润总额F17_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算平均值,万元
EST_TOTAL_PROFIT_MEDIAN,预测利润总额中值,Median total profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的利润总额F17_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取中值,万元
EST_TOTAL_PROFIT_DIF,预测利润总额标准差,Standard deviation of total profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的利润总额F17_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算标准差,万元
EST_TOTAL_PROFIT_MAX,预测利润总额最大值,Maximum profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的利润总额F17_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最大值,万元
EST_TOTAL_PROFIT_MIN,预测利润总额最小值,Minimum profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的利润总额F17_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最小值,万元
RETURN_ASSETS,预测总资产收益率平均值,Return on total assets,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的总资产收益率F18_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算平均值,%
RETURN_ASSETS_MEDIAN,预测总资产收益率(ROA)中值,Median return on total assets,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的总资产收益率F18_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取中值,%
RETURN_ASSETS_DIF,预测总资产收益率标准差,Standard deviation of total return on assets,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的总资产收益率F18_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算标准差,%
RETURN_ASSETS_MAX,预测总资产收益率最大值,Maximum return on total assets,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的总资产收益率F18_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最大值,%
RETURN_ASSETS_MIN,预测总资产收益率最小值,Minimum return on total assets,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的总资产收益率F18_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最小值,%
EST_ROE,预测净资产收益率平均值,Roe,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净资产收益率F19_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算平均值,%
EST_ROE_MEDIAN,预测净资产收益率中值,Roe Median,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净资产收益率F19_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取中值,%
EST_ROE_DIF,预测净资产收益率标准差,Roe Standard deviation,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净资产收益率F19_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算标准差,%
EST_ROE_MAX,预测净资产收益率最大值,Roe Maximum,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净资产收益率F19_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最大值,%
EST_ROE_MIN,预测净资产收益率最小值,Roe Minimum value,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的净资产收益率F19_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最小值,%
EST_OPER_PROFIT,预测营业利润平均值,operating profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的营业利润F20_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算平均值,万元
EST_OPER_PROFIT_MEDIAN,预测营业利润中值,Median operating profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的营业利润F20_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取中值,万元
EST_OPER_PROFIT_DIF,预测营业利润标准差,Operating profit standard deviation,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的营业利润F20_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），计算标准差,万元
EST_OPER_PROFIT_MAX,预测营业利润最大值,Maximum operating profit,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的营业利润F20_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最大值,万元
EST_OPER_PROFIT_MIN,预测营业利润最小值,Operating profit minimum,"NUMBER(20,4)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录的营业利润F20_1571（该记录需日期在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），取最小值,万元
EST_EPS_NUM,每股收益预测机构家数,Earnings per share forecast,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布每股收益F6_1571，统计机构家数,家
EST_OPER_REVENUE_NUM,主营业务收入预测家数,Main business income forecast number,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布营业收入F7_1571，统计机构家数,家
NET_PROFIT_NUM,净利润预测家数,Net profit forecast number,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布净利润F8_1571，统计机构家数,家
EST_CFPS_NUM,每股现金流预测家数,Cash flow forecaster per share,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布每股现金流F12_1571，统计机构家数,家
EST_DPS_NUM,每股股利预测家数,Dividend forecast per share,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布每股股利F13_1571，统计机构家数,家
EST_EBIT_NUM,息税前利润预测家数,EBIT forecast,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布息税前利润F14_1571，统计机构家数,家
EST_EBITDA_NUM,息税折旧摊销前利润预测家数,EBITDA Number of forecasters,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布息税折旧摊销前利润F15_1571，统计机构家数,家
EST_BPS_NUM,每股净资产预测家数,Net asset forecast per share,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布每股净资产F16_1571，统计机构家数,家
EST_TOTAL_PROFIT_NUM,利润总额预测家数,Total profit forecast,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布利润总额F17_1571，统计机构家数,家
RETURN_ASSETS_NUM,总资产收益率预测家数,Total return on assets forecast,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布总资产收益率F18_1571，统计机构家数,家
EST_ROE_NUM,净资产收益率预测家数,Return on net assets forecast,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布净资产收益率F19_1571，统计机构家数,家
EST_OPER_PROFIT_NUM,营业利润预测家数,Operating profit forecast number,"NUMBER(20,0)",以“T日”向前寻找180天内各个机构对某公司某报告期最新一次的预测记录（该记录日期需在180天范围内），且综合值计算标记为1（是否参与一致预测计算F21_1571=1），公布营业利润F20_1571，统计机构家数,家
