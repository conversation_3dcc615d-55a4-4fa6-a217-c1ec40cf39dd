﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),万得自定义的用来识别证券的唯一编码；证券产品在万得库中对应的唯一标识ID；万得定义的用来识别证券的内部唯一编码；,
S_INFO_COMPCODE,交易代码,trasaction code,VARCHAR2(40),万得自定义的用来识别公司的唯一编码,
STAFF_TYPE_CODE,人数类别代码,Number category code,"NUMBER(9,0)",万得自定义的用来识别数据是从业人数还是员工人数的编码,
END_DT,截止日期,deadline,VARCHAR2(8),数据统计区间的最晚日期,
REPORT_TYPE_CODE,报告类型代码,Report type code,"NUMBER(9,0)",万得自定义的用来区别数据来源报告类型的编码,
ITEM_TYPE_CODE,项目分类代码,Project classification code,"NUMBER(9,0)",万得自定义的用来识别数据分类的编码,
ITEM_NAME,项目,project,VARCHAR2(100),公告公布的数据名称,
ITEM_CODE,项目代码,Project code,"NUMBER(9,0)",万得自定义的用来识别数据名称的编码,
STAFF_NUMBER,人数,Number of people,"NUMBER(20,0)",公告公布的人员数量,人
PROPORTION,所占比例,The proportion,"NUMBER(20,4)",人员数量占所有员工数量的百分比,%
ANN_DT,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),"万得自定义的用来识别证券的唯一编码,后缀为交易场所",
