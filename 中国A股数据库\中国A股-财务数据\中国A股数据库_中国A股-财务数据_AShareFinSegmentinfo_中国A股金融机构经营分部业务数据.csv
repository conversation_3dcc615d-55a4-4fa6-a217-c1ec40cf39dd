﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(100),,
S_INFO_COMPCODE,公司ID,Company ID,VARCHAR2(10),万得自定义的用来识别公司的唯一编码,
REPORT_PERIOD,报告期,Reporting period,VARCHAR2(8),反映企业经营成果、现金流量的特定时期根据长短不同分为年度、半年度、季度,
STATEMENT_TYPE,报表类型代码,Report type code,"NUMBER(9,0)",自编代码以408开头，代表报表的分类，可通过关联AShareTypeCode.S_ORIGIN_TYPCODE获取分类名称(S_TYPNAME),
CLASS_CODE,分部类别代码,Division category code,"NUMBER(9,0)","自编代码以209开头，代表地区分部/业务分部等类别,可通过关联AShareTypeCode.S_ORIGIN_TYPCODE获取分类名称(S_TYPNAME)",
SUBJECT_CODE,科目代码,Account code,"NUMBER(9,0)","自编代码以210开头，代表规范后的科目分类，例如营业收入、利息净收入,可通过关联AShareTypeCode.S_ORIGIN_TYPCODE获取分类名称(S_TYPNAME)",
ITEM_AMOUNT,金额,Amount,"NUMBER(20,4)",公告公布的与此相关的具体名称金额，,
UNIT,单位,unit,VARCHAR2(40),对应金额字段的单位,
CRNCY_CODE,货币代码,Currency code,VARCHAR2(10),自编代码，对应金额字段的货币代码,
SUBJECT_NAME_ANN,[内部]公布科目名称,Announce the name of the subject,VARCHAR2(100),对应科目代码字段，记录财报中公布的科目名称，作为参考,
