﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object ID,VARCHAR2(38),,
S_INFO_COMPCODE,公司id,Company id,VARCHAR2(10),万得自定义的用来识别公司的唯一编码,
ANN_DATE,公告日期,Announcement date,VARCHAR2(8),公告发布当天的日期,
S_FRO_BGDATE,冻结起始时间,Freeze start time,VARCHAR2(8),股东股份被冻结的起始日期,
S_FRO_ENDDATE,冻结结束时间,Freeze end time,VARCHAR2(8),股东股份被冻结的结束日期,
S_HOLDER_NAME,股东名称,Shareholder Name,VARCHAR2(200),股份被冻结的股东名称,
S_FRO_SHARES,冻结数量(万股),"Number of freezes (10,000 shares)","NUMBER(20,4)",本次冻结（解冻）的股份数量,万股
FROZEN_INSTITUTION,执行冻结机构,Execution Freeze Mechanism,VARCHAR2(200),股份被冻结的执行机构,
DISFROZEN_TIME,解冻日期,Thaw date,VARCHAR2(8),股东股份被解除冻结的日期,
S_HOLDER_TYPE_CODE,股东类型代码,Shareholder type code,"NUMBER(9,0)","*********  公司
*********  个人",
S_HOLDER_ID,股东ID,Shareholder ID,VARCHAR2(10),万得自定义的用来识别股东的唯一编码,
SHR_CATEGORY_CODE,股份性质类别代码,Stock nature category code,"NUMBER(9,0)","460001000	法人股
460002000	个人股
460003000	国有股
460004000	国有股,法人股
460005000	流通股
460006000	流通股,限售流通股
460007000	外资股
460008000	限售流通股
*********	优先股",
IS_TURN_FROZEN,是否轮候冻结,Whether waiting for the freeze,"NUMBER(1,0)",判断股份被冻结的方式是否为轮候冻结，1为是，0位否,
IS_DISFROZEN,是否解冻,Whether to thaw,"NUMBER(1,0)",判断是否为解除冻结股份，1为是，0为否,
S_TOTAL_HOLDING_SHR,持股总数（万股）,"Total shares held (10,000 shares)","NUMBER(20,4)",股东持有的股份数量,万股
S_FRO_SHR_RATIO,本次冻结股数占公司总股本比例,The number of frozen shares accounted for the company's total share capital ratio,"NUMBER(20,4)",本次冻结（解冻）的股份数量占上市公司总股本的比例,%
S_TOTAL_HOLDING_SHR_RATIO,持股总数占公司总股本比例,The total number of shares held by the company's total share capital ratio,"NUMBER(20,4)",股东持有的股份数量占上市公司总股本的比例,%
S_FRO_HOLDSHR_RATIO,本次冻结股数占其所持股份比例,The Number Of Frozen Shares Accounted For The Holder's Total Share Capital Ratio,"NUMBER(20,4)",本次冻结（解冻）的股份数量占股东持有股份数量的比例,%
S_TOTAL_FRO_HOLDSHR_RATIO,累计冻结数量占其所持股份比例,The Total Number Of Frozen Shares By The Holder's Total Share Capital Ratio,"NUMBER(20,4)",股东累计被冻结的股份数量占股东持有股份数量的比例,%
