#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行批量数据版本的可转债策略
"""

from convertible_bond_strategy_batch import ConvertibleBondStrategyBatch
from pathlib import Path

def quick_demo():
    """快速演示"""
    print("=" * 80)
    print("可转债量化策略 - 批量数据版本快速演示")
    print("=" * 80)
    
    # 创建短期测试策略
    print("创建策略实例（2024年1-3月测试）...")
    strategy = ConvertibleBondStrategyBatch('2024-01-01', '2024-03-31')
    
    # 检查本地数据
    data_folder = Path('BatchData')
    has_data = data_folder.exists() and any(data_folder.glob('*.pkl'))
    
    if not has_data:
        print("\n本地没有数据，开始获取数据...")
        print("注意：首次运行需要较长时间获取数据")
        
        confirm = input("是否继续获取数据？(y/n): ").strip().lower()
        if confirm != 'y':
            print("取消演示")
            return
        
        # 获取数据
        success = strategy.fetch_all_data()
        if not success:
            print("数据获取失败，演示结束")
            return
    else:
        print("使用本地已有数据...")
    
    # 运行回测
    print("\n开始运行回测...")
    strategy.run_backtest()
    
    print("\n演示完成！")

def main():
    """主程序"""
    print("可转债量化策略 - 批量数据版本")
    print("=" * 60)
    print("特点：")
    print("- 先批量获取所有数据到本地")
    print("- 避免重复查询数据库")
    print("- 提高回测效率")
    print("- 支持离线分析")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 快速演示（2024年1-3月）")
        print("2. 完整功能菜单")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            quick_demo()
        elif choice == '2':
            # 调用主策略的菜单
            from convertible_bond_strategy_batch import main as batch_main
            batch_main()
        elif choice == '3':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
