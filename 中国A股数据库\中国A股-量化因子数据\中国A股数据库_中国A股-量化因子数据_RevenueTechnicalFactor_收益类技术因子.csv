﻿FieldName,FieldCnName,FieldEnName,FieldType,Define,Unit
OBJECT_ID,对象ID,Object id,VARCHAR2(100),主键,
S_INFO_WINDCODE,Wind代码,Wind code,VARCHAR2(40),证券交易代码,
TRADE_DT,交易日期,Date of transaction,VARCHAR2(8),指定交易日期,
s_risk_Earning_day,每日收益,Daily earnings,"NUMBER(20,8)",范围包括股票、沪深300指数和一年期国债收益率,
s_risk_variance20,20日收益方差,20-day earnings variance,"NUMBER(20,8)",结果为年化后的值,
s_risk_variance30,30日收益方差,30-day earnings variance,"NUMBER(20,8)",结果为年化后的值,
s_risk_variance60,60日收益方差,60-day earnings variance,"NUMBER(20,8)",结果为年化后的值,
s_risk_variance120,120日收益方差,120-day earnings variance,"NUMBER(20,8)",结果为年化后的值,
s_risk_gainvariance20,20日正收益方差,20-day positive return variance,"NUMBER(20,8)",20日内收益为正时的方差,
s_risk_gainvariance60,60日正收益方差,60-day positive return variance,"NUMBER(20,8)",60日内收益为正时的方差,
s_risk_gainvariance120,120日正收益方差,120-day positive return variance,"NUMBER(20,8)",120日内收益为正时的方差,
s_risk_lossvariance20,20日损失方差,20-day loss variance,"NUMBER(20,8)",20日内收益为负时的方差,
s_risk_lossvariance60,60日损失方差,60-day loss variance,"NUMBER(20,8)",60日内收益为负时的方差,
s_risk_lossvariance120,120日损失方差,120-day loss variance,"NUMBER(20,8)",120日内收益为负时的方差,
s_risk_GLvarianceratio20,20日收益损失方差比,20-day loss-of-return variance ratio,"NUMBER(20,8)",20日内收益为负时的方差,
s_risk_GLvarianceRatio60,60日收益损失方差比,60-day loss-of-return variance ratio,"NUMBER(20,8)",60日内收益为负时的方差,
s_risk_GLvarianceRatio120,120日收益损失方差比,120-day earnings loss variance ratio,"NUMBER(20,8)",120日内收益为正时的方差,
s_risk_REVSVarRatio,30日120日回报方差比率,30-day 120-day return variance ratio,"NUMBER(20,8)",30日收益率方差/120日收益率方差,
s_risk_kurtosis20,个股收益的20日峰度,20-day peak of individual stock returns,"NUMBER(20,8)",四阶矩,
s_risk_kurtosis60,个股收益的60日峰度,60-day peak of individual stock returns,"NUMBER(20,8)",四阶矩,
s_risk_kurtosis120,个股收益的120日峰度,120-day peak of individual stock returns,"NUMBER(20,8)",四阶矩,
s_risk_Beta20,个股20日的beta值,Beta value of 20 stocks,"NUMBER(20,8)",Beta系数是用来衡量两个时间序列之间关系的统计指标。在金融数据的分析中，Beta用来衡量个股相对于市场的风险,
s_risk_Beta60,个股60日的beta值,Beta value of stocks for 60 days,"NUMBER(20,8)",Beta系数是用来衡量两个时间序列之间关系的统计指标。在金融数据的分析中，Beta用来衡量个股相对于市场的风险,
s_risk_Beta120,个股120日的beta值,Beta value of 120 stocks,"NUMBER(20,8)",Beta系数是用来衡量两个时间序列之间关系的统计指标。在金融数据的分析中，Beta用来衡量个股相对于市场的风险,
s_risk_treynorRatio20,20日特诺雷比率,Twenty-day trevor ratio,"NUMBER(20,8)",(年化后的20日平均收益率-无风险收益率)／20日Beta,
s_risk_treynorRatio60,60日特诺雷比率,60-day trevor ratio,"NUMBER(20,8)",(年化后的60日平均收益率-无风险收益率)／60日Beta,
s_risk_treynorRatio120,120日特诺雷比率,120-day trevor ratio,"NUMBER(20,8)",(年化后的120日平均收益率-无风险收益率)／120日Beta,
S_TECH_REVS5,过去5日的价格动量,Price momentum over the past five days,"NUMBER(20,8)",5日收益累加，属于超买超卖型因子,
S_TECH_REVS10,过去10日的价格动量,Price momentum over the past 10 days,"NUMBER(20,8)",10日收益累加，属于超买超卖型因子,
s_tech_REVS20,过去1个月的价格动量,Price momentum over the past month,"NUMBER(20,8)",20日收益累加，属于超买超卖型因子,
s_tech_REVS60,过去3个月的价格动量,Price momentum over the past three months,"NUMBER(20,8)",60日收益累加，属于超买超卖类因子,
s_tech_REVS120,过去6个月的价格动量,Price momentum over the past six months,"NUMBER(20,8)",120日收益累加，属于超买超卖类因子,
s_tech_REVS250,过去1年的价格动量,Price momentum over the past year,"NUMBER(20,8)",250日收益累加，属于超买超卖类因子,
s_tech_REVS750,过去3年的价格动量,Price momentum over the past three years,"NUMBER(20,8)",750日收益累加，属于超买超卖类因子,
S_TECH_REVS5M20,过去5日的价格动量-过去1个月的价格动量,Price momentum of the past 5 days - price momentum of the past 1 month,"NUMBER(20,8)",过去5日的价格动量-过去1个月的价格动量；5日的价格动量=5天内累计收益率之和，过去1个月的价格动量=1月内累计收益率之和,
S_TECH_REVS5M60,过去5日的价格动量-过去3个月的价格动量,Price momentum of the past 5 days - price momentum of the past 3 months,"NUMBER(20,8)",过去5日的价格动量-过去3个月的价格动量；5日的价格动量=5天内累计收益率之和，过去3个月的价格动量=3月内累计收益率之和,
S_TECH_RANK1M,1-过去一个月收益率排名/股票总数的比值,1 - ratio of return rankings to total stocks over the past month,"NUMBER(20,8)",1-过去一个月收益率排名/股票总数的比值；股票总数范围为沪深市场的所有股票，全部A股；,
s_tech_REVS6m20,过去6个月的动量-过去1个月的动量,Momentum in the past six months - momentum in the past one month,"NUMBER(20,8)",过去6个月的动量-过去1个月的动量,
s_tech_REVS12m20,过去12个月的动量-过去1个月的动量,Momentum in the past 12 months - momentum in the past 1 month,"NUMBER(20,8)",过去12个月的动量-过去1个月的动量,
s_tech_REVS12m6m,12M收益率的120D变化率,120d rate of change of 12m return rate,"NUMBER(20,8)",(1+12M收益率)/(1+6个月前的12M收益率)-1,
s_tech_REVS1mMAX,过去1个月的日收益率的最大值,Maximum daily return over the past month,"NUMBER(20,8)",范围包括股票、沪深300指数和一年期国债收益率,
